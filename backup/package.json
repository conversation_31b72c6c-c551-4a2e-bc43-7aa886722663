{"name": "editable-website", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write ."}, "devDependencies": {"@sveltejs/adapter-auto": "^2.0.0", "@sveltejs/kit": "^1.20.5", "eslint": "^8.28.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-svelte3": "^4.0.0", "postcss": "^8.4.21", "prettier": "^2.8.0", "prettier-plugin-svelte": "^2.8.1", "svelte": "^3.54.0", "vite": "4.2.3"}, "type": "module", "dependencies": {"@sveltejs/adapter-node": "^1.2.4", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.9", "autoprefixer": "^10.4.14", "@fontsource/jost": "^4.5.13", "better-sqlite3": "^8.4.0", "camelcase-keys": "^8.0.2", "nanoid": "^4.0.2", "prosemirror-commands": "^1.5.0", "prosemirror-dropcursor": "^1.6.1", "prosemirror-example-setup": "^1.2.1", "prosemirror-gapcursor": "^1.3.1", "prosemirror-history": "^1.3.0", "prosemirror-inputrules": "^1.2.0", "prosemirror-keymap": "^1.2.0", "prosemirror-model": "^1.19.0", "prosemirror-schema-basic": "^1.2.1", "prosemirror-schema-list": "^1.2.2", "prosemirror-state": "^1.4.2", "prosemirror-view": "^1.30.1", "slugify": "^1.6.5", "tailwindcss": "^3.3.1"}}