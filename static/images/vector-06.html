<!DOCTYPE html>
<html>
<head>
  <title>Vector Image</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: transparent;
    }
    .circle-container {
      position: relative;
      width: 450px;
      height: 450px;
    }
    .circle {
      position: absolute;
      border-radius: 50%;
      background-color: #4975f1;
    }
    .circle-1 {
      width: 400px;
      height: 400px;
      top: 25px;
      left: 25px;
      opacity: 0.3;
    }
    .circle-2 {
      width: 300px;
      height: 300px;
      top: 75px;
      left: 75px;
      opacity: 0.2;
    }
    .circle-3 {
      width: 200px;
      height: 200px;
      top: 125px;
      left: 125px;
      opacity: 0.1;
    }
  </style>
</head>
<body>
  <div class="circle-container">
    <div class="circle circle-1"></div>
    <div class="circle circle-2"></div>
    <div class="circle circle-3"></div>
  </div>
</body>
</html>
