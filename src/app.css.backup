@tailwind base; @tailwind components; @tailwind utilities;

html{@apply text-gray-900;}

/* Custom utility classes with !important to override other styles */
.mt-0-important {
  margin-top: 0 !important;
}
.bounce{animation:bounce 2s infinite;}
@keyframes bounce{
  0%,20%,50%,80%,100%{transform:translateY(0);}
  40%{transform:translateY(-10px);}
  60%{transform:translateY(-5px);}
}
/* Prosemirror styles */
.ProseMirror{position:relative;}
.ProseMirror{word-wrap:break-word; white-space:pre-wrap; white-space:break-spaces; -webkit-font-variant-ligatures:none; font-variant-ligatures:none; font-feature-settings:"liga" 0; /* the above doesn't seem to work in Edge */}
.ProseMirror pre{white-space:pre-wrap;}
.ProseMirror li{position:relative;}
.ProseMirror-hideselection *::selection{background:transparent;}
.ProseMirror-hideselection *::-moz-selection{background:transparent;}
.ProseMirror-hideselection{caret-color:transparent;}
.ProseMirror-selectednode{outline:2px solid #8cf;}
/* Make sure li selections wrap around markers */
li.ProseMirror-selectednode{outline:none;}
li.ProseMirror-selectednode:after{content:""; position:absolute; left:-32px; right:-2px; top:-2px; bottom:-2px; border:2px solid #8cf; pointer-events:none;}
/* My Styles */

/* html{-webkit-text-size-adjust:100%; -ms-text-size-adjust:100%; font-family:sans-serif;}
body{margin:0;}
*/
article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block;}
audio,canvas,progress,video{vertical-align:baseline; display:inline-block;}
audio:not([controls]){height:0; display:none;}
[hidden],template{display:none;}
a{background-color:#0000; text-decoration:none !important;}
a:active,a:hover{outline:0;}
abbr[title]{border-bottom:1px dotted;}
b,strong{font-weight:bold;}
dfn{font-style:italic;}
h1{margin:.67em 0; font-size:2em;}
mark{color:#000; background:#ff0;}
small{font-size:80%;}
sub,sup{vertical-align:baseline; font-size:75%; line-height:0; position:relative;}
sup{top:-.5em;}
sub{bottom:-.25em;}
img{border:0;}
svg:not(:root){overflow:hidden;}
hr{box-sizing:content-box; height:0;}
pre{overflow:auto;}
code,kbd,pre,samp{font-family:monospace; font-size:1em;}
button,input,optgroup,select,textarea{color:inherit; font:inherit; margin:0;}
button{overflow:visible;}
button,select{text-transform:none;}
button,html input[type="button"],input[type="reset"]{-webkit-appearance:button; cursor:pointer;}
button[disabled],html input[disabled]{cursor:default;}
button::-moz-focus-inner,input::-moz-focus-inner{border:0; padding:0;}
input{line-height:normal;}
input[type="checkbox"],input[type="radio"]{box-sizing:border-box; padding:0;}
input[type="number"]::-webkit-inner-spin-button,input[type="number"]::-webkit-outer-spin-button{height:auto;}
input[type="search"]{-webkit-appearance:none;}
input[type="search"]::-webkit-search-cancel-button,input[type="search"]::-webkit-search-decoration{-webkit-appearance:none;}
legend{border:0; padding:0;}
textarea{overflow:auto;}
optgroup{font-weight:bold;}
table{border-collapse:collapse; border-spacing:0;}
td,th{padding:0;}
@font-face{font-family:webflow-icons; src:url("data:application/x-font-ttf; charset=utf-8; base64,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") format("truetype"); font-weight:normal; font-style:normal;}
[class^="w-icon-"],[class*=" w-icon-"]{speak:none; font-variant:normal; text-transform:none; -webkit-font-smoothing:antialiased; -moz-osx-font-smoothing:grayscale; font-style:normal; font-weight:normal; line-height:1; font-family:webflow-icons !important;}
.w-icon-slider-right:before{content:"î˜€";}
.w-icon-slider-left:before{content:"î˜";}
.w-icon-nav-menu:before{content:"î˜‚";}
.w-icon-arrow-down:before,.w-icon-dropdown-toggle:before{content:"î˜ƒ";}
.w-icon-file-upload-remove:before{content:"î¤€";}
.w-icon-file-upload-icon:before{content:"î¤ƒ";}
*{box-sizing:border-box;}
html{height:100%;}
body{height:100%;}
img{vertical-align:middle; max-width:100%; display:inline-block;}
html.w-mod-touch *{background-attachment:scroll !important;}
.w-block{display:block;}
.w-inline-block{max-width:100%; display:inline-block;}
.w-clearfix:before,.w-clearfix:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-clearfix:after{clear:both;}
.w-hidden{display:none;}
.w-button{color:#fff; line-height:inherit; cursor:pointer; background-color:#3898ec; border:0; border-radius:0; padding:9px 15px; text-decoration:none; display:inline-block;}
input.w-button{-webkit-appearance:button;}
html[data-w-dynpage] [data-w-cloak]{color:#0000 !important;}
.w-code-block{margin:unset;}
pre.w-code-block code{all:inherit;}
.w-optimization{display:contents;}
.w-webflow-badge,.w-webflow-badge > img{box-sizing:unset; width:unset; height:unset; max-height:unset; max-width:unset; min-height:unset; min-width:unset; margin:unset; padding:unset; float:unset; clear:unset; border:unset; border-radius:unset; background:unset; background-image:unset; background-position:unset; background-size:unset; background-repeat:unset; background-origin:unset; background-clip:unset; background-attachment:unset; background-color:unset; box-shadow:unset; transform:unset; direction:unset; font-family:unset; font-weight:unset; color:unset; font-size:unset; line-height:unset; font-style:unset; font-variant:unset; text-align:unset; letter-spacing:unset; -webkit-text-decoration:unset; text-decoration:unset; text-indent:unset; text-transform:unset; list-style-type:unset; text-shadow:unset; vertical-align:unset; cursor:unset; white-space:unset; word-break:unset; word-spacing:unset; word-wrap:unset; transition:unset;}
.w-webflow-badge{white-space:nowrap; cursor:pointer; box-shadow:0 0 0 1px #0000001a,0 1px 3px #0000001a; visibility:visible !important; opacity:1 !important; z-index:2147483647 !important; color:#aaadb0 !important; overflow:unset !important; background-color:#fff !important; border-radius:3px !important; width:auto !important; height:auto !important; margin:0 !important; padding:6px !important; font-size:12px !important; line-height:14px !important; text-decoration:none !important; display:inline-block !important; position:fixed !important; inset:auto 12px 12px auto !important; transform:none !important;}
.w-webflow-badge > img{position:unset; visibility:unset !important; opacity:1 !important; vertical-align:middle !important; display:inline-block !important;}
h1,h2,h3,h4,h5,h6{margin-bottom:10px; font-weight:bold;}
h1{margin-top:20px; font-size:38px; line-height:44px;}
h2{margin-top:20px; font-size:32px; line-height:36px;}
h3{margin-top:20px; font-size:24px; line-height:30px;}
h4{margin-top:10px; font-size:18px; line-height:24px;}
h5{margin-top:10px; font-size:14px; line-height:20px;}
h6{margin-top:10px; font-size:12px; line-height:18px;}
p{margin-top:0; margin-bottom:10px;}
blockquote{border-left:5px solid #e2e2e2; margin:0 0 10px; padding:10px 20px; font-size:18px; line-height:22px;}
figure{margin:0 0 10px;}
ul,ol{margin-top:0; margin-bottom:10px; padding-left:40px;}
.w-list-unstyled{padding-left:0; list-style:none;}
.w-embed:before,.w-embed:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-embed:after{clear:both;}
.w-video{width:100%; padding:0; position:relative;}
.w-video iframe,.w-video object,.w-video embed{border:none; width:100%; height:100%; position:absolute; top:0; left:0;}
fieldset{border:0; margin:0; padding:0;}
button,[type="button"],[type="reset"]{cursor:pointer; -webkit-appearance:button;}
.w-form{margin:0 0 15px;}
.w-form-done{text-align:center; background-color:#ddd; padding:20px; display:none;}
.w-form-fail{background-color:#ffdede; margin-top:10px; padding:10px; display:none;}
.w-input,.w-select{color:#333; vertical-align:middle; background-color:#fff; border:1px solid #ccc; width:100%; height:38px; margin-bottom:10px; padding:8px 12px; font-size:14px; line-height:1.42857; display:block;}
.w-input::placeholder,.w-select::placeholder{color:#999;}
.w-input:focus,.w-select:focus{border-color:#3898ec; outline:0;}
.w-input[disabled],.w-select[disabled],.w-input[readonly],.w-select[readonly],fieldset[disabled] .w-input,fieldset[disabled] .w-select{cursor:not-allowed;}
.w-input[disabled]:not(.w-input-disabled),.w-select[disabled]:not(.w-input-disabled),.w-input[readonly],.w-select[readonly],fieldset[disabled]:not(.w-input-disabled) .w-input,fieldset[disabled]:not(.w-input-disabled) .w-select{background-color:#eee;}
textarea.w-input,textarea.w-select{height:auto;}
.w-select{background-color:#f3f3f3;}
.w-select[multiple]{height:auto;}
.w-form-label{cursor:pointer; margin-bottom:0; font-weight:normal; display:inline-block;}
.w-radio{margin-bottom:5px; padding-left:20px; display:block;}
.w-radio:before,.w-radio:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-radio:after{clear:both;}
.w-radio-input{float:left; margin:3px 0 0 -20px; line-height:normal;}
.w-file-upload{margin-bottom:10px; display:block;}
.w-file-upload-input{opacity:0; z-index:-100; width:.1px; height:.1px; position:absolute; overflow:hidden;}
.w-file-upload-default,.w-file-upload-uploading,.w-file-upload-success{color:#333; display:inline-block;}
.w-file-upload-error{margin-top:10px; display:block;}
.w-file-upload-default.w-hidden,.w-file-upload-uploading.w-hidden,.w-file-upload-error.w-hidden,.w-file-upload-success.w-hidden{display:none;}
.w-file-upload-uploading-btn{cursor:pointer; background-color:#fafafa; border:1px solid #ccc; margin:0; padding:8px 12px; font-size:14px; font-weight:normal; display:flex;}
.w-file-upload-file{background-color:#fafafa; border:1px solid #ccc; flex-grow:1; justify-content:space-between; margin:0; padding:8px 9px 8px 11px; display:flex;}
.w-file-upload-file-name{font-size:14px; font-weight:normal; display:block;}
.w-file-remove-link{cursor:pointer; width:auto; height:auto; margin-top:3px; margin-left:10px; padding:3px; display:block;}
.w-icon-file-upload-remove{margin:auto; font-size:10px;}
.w-file-upload-error-msg{color:#ea384c; padding:2px 0; display:inline-block;}
.w-file-upload-info{padding:0 12px; line-height:38px; display:inline-block;}
.w-file-upload-label{cursor:pointer; background-color:#fafafa; border:1px solid #ccc; margin:0; padding:8px 12px; font-size:14px; font-weight:normal; display:inline-block;}
.w-icon-file-upload-icon,.w-icon-file-upload-uploading{width:20px; margin-right:8px; display:inline-block;}
.w-icon-file-upload-uploading{height:20px;}
.w-container{max-width:940px; margin-left:auto; margin-right:auto;}
.w-container:before,.w-container:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-container:after{clear:both;}
.w-container .w-row{margin-left:-10px; margin-right:-10px;}
.w-row:before,.w-row:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-row:after{clear:both;}
.w-row .w-row{margin-left:0; margin-right:0;}
.w-col{float:left; width:100%; min-height:1px; padding-left:10px; padding-right:10px; position:relative;}
.w-col .w-col{padding-left:0; padding-right:0;}
.w-col-1{width:8.33333%;}
.w-col-2{width:16.6667%;}
.w-col-3{width:25%;}
.w-col-4{width:33.3333%;}
.w-col-5{width:41.6667%;}
.w-col-6{width:50%;}
.w-col-7{width:58.3333%;}
.w-col-8{width:66.6667%;}
.w-col-9{width:75%;}
.w-col-10{width:83.3333%;}
.w-col-11{width:91.6667%;}
.w-col-12{width:100%;}
.w-hidden-main{display:none !important;}
@media screen and (max-width:991px){
  .w-container{max-width:728px;}
  .w-hidden-main{display:inherit !important;}
  .w-hidden-medium{display:none !important;}
  .w-col-medium-1{width:8.33333%;}
  .w-col-medium-2{width:16.6667%;}
  .w-col-medium-3{width:25%;}
  .w-col-medium-4{width:33.3333%;}
  .w-col-medium-5{width:41.6667%;}
  .w-col-medium-6{width:50%;}
  .w-col-medium-7{width:58.3333%;}
  .w-col-medium-8{width:66.6667%;}
  .w-col-medium-9{width:75%;}
  .w-col-medium-10{width:83.3333%;}
  .w-col-medium-11{width:91.6667%;}
  .w-col-medium-12{width:100%;}
  .w-col-stack{width:100%; left:auto; right:auto;}
}
@media screen and (max-width:767px){
  .w-hidden-main,.w-hidden-medium{display:inherit !important;}
  .w-hidden-small{display:none !important;}
  .w-row,.w-container .w-row{margin-left:0; margin-right:0;}
  .w-col{width:100%; left:auto; right:auto;}
  .w-col-small-1{width:8.33333%;}
  .w-col-small-2{width:16.6667%;}
  .w-col-small-3{width:25%;}
  .w-col-small-4{width:33.3333%;}
  .w-col-small-5{width:41.6667%;}
  .w-col-small-6{width:50%;}
  .w-col-small-7{width:58.3333%;}
  .w-col-small-8{width:66.6667%;}
  .w-col-small-9{width:75%;}
  .w-col-small-10{width:83.3333%;}
  .w-col-small-11{width:91.6667%;}
  .w-col-small-12{width:100%;}
}
@media screen and (max-width:479px){
  .w-container{max-width:none;}
  .w-hidden-main,.w-hidden-medium,.w-hidden-small{display:inherit !important;}
  .w-hidden-tiny{display:none !important;}
  .w-col{width:100%;}
  .w-col-tiny-1{width:8.33333%;}
  .w-col-tiny-2{width:16.6667%;}
  .w-col-tiny-3{width:25%;}
  .w-col-tiny-4{width:33.3333%;}
  .w-col-tiny-5{width:41.6667%;}
  .w-col-tiny-6{width:50%;}
  .w-col-tiny-7{width:58.3333%;}
  .w-col-tiny-8{width:66.6667%;}
  .w-col-tiny-9{width:75%;}
  .w-col-tiny-10{width:83.3333%;}
  .w-col-tiny-11{width:91.6667%;}
  .w-col-tiny-12{width:100%;}
}
.w-widget{position:relative;}
.w-widget-map{width:100%; height:400px;}
.w-widget-map label{width:auto; display:inline;}
.w-widget-map img{max-width:inherit;}
.w-widget-map .gm-style-iw{text-align:center;}
.w-widget-map .gm-style-iw > button{display:none !important;}
.w-widget-twitter{overflow:hidden;}
.w-widget-twitter-count-shim{vertical-align:top; text-align:center; background:#fff; border:1px solid #758696; border-radius:3px; width:28px; height:20px; display:inline-block; position:relative;}
.w-widget-twitter-count-shim *{pointer-events:none; -webkit-user-select:none; user-select:none;}
.w-widget-twitter-count-shim .w-widget-twitter-count-inner{text-align:center; color:#999; font-family:serif; font-size:15px; line-height:12px; position:relative;}
.w-widget-twitter-count-shim .w-widget-twitter-count-clear{display:block; position:relative;}
.w-widget-twitter-count-shim.w--large{width:36px; height:28px;}
.w-widget-twitter-count-shim.w--large .w-widget-twitter-count-inner{font-size:18px; line-height:18px;}
.w-widget-twitter-count-shim:not(.w--vertical){margin-left:5px; margin-right:8px;}
.w-widget-twitter-count-shim:not(.w--vertical).w--large{margin-left:6px;}
.w-widget-twitter-count-shim:not(.w--vertical):before,.w-widget-twitter-count-shim:not(.w--vertical):after{content:" "; pointer-events:none; border:solid #0000; width:0; height:0; position:absolute; top:50%; left:0;}
.w-widget-twitter-count-shim:not(.w--vertical):before{border-width:4px; border-color:#75869600 #5d6c7b #75869600 #75869600; margin-top:-4px; margin-left:-9px;}
.w-widget-twitter-count-shim:not(.w--vertical).w--large:before{border-width:5px; margin-top:-5px; margin-left:-10px;}
.w-widget-twitter-count-shim:not(.w--vertical):after{border-width:4px; border-color:#fff0 #fff #fff0 #fff0; margin-top:-4px; margin-left:-8px;}
.w-widget-twitter-count-shim:not(.w--vertical).w--large:after{border-width:5px; margin-top:-5px; margin-left:-9px;}
.w-widget-twitter-count-shim.w--vertical{width:61px; height:33px; margin-bottom:8px;}
.w-widget-twitter-count-shim.w--vertical:before,.w-widget-twitter-count-shim.w--vertical:after{content:" "; pointer-events:none; border:solid #0000; width:0; height:0; position:absolute; top:100%; left:50%;}
.w-widget-twitter-count-shim.w--vertical:before{border-width:5px; border-color:#5d6c7b #75869600 #75869600; margin-left:-5px;}
.w-widget-twitter-count-shim.w--vertical:after{border-width:4px; border-color:#fff #fff0 #fff0; margin-left:-4px;}
.w-widget-twitter-count-shim.w--vertical .w-widget-twitter-count-inner{font-size:18px; line-height:22px;}
.w-widget-twitter-count-shim.w--vertical.w--large{width:76px;}
.w-background-video{color:#fff; height:500px; position:relative; overflow:hidden;}
.w-background-video > video{object-fit:cover; z-index:-100; background-position:50%; background-size:cover; width:100%; height:100%; margin:auto; position:absolute; inset:-100%;}
.w-background-video > video::-webkit-media-controls-start-playback-button{-webkit-appearance:none; display:none !important;}
.w-background-video--control{background-color:#0000; padding:0; position:absolute; bottom:1em; right:1em;}
.w-background-video--control > [hidden]{display:none !important;}
.w-slider{text-align:center; clear:both; -webkit-tap-highlight-color:#0000; tap-highlight-color:#0000; background:#ddd; height:300px; position:relative;}
.w-slider-mask{z-index:1; white-space:nowrap; height:100%; display:block; position:relative; left:0; right:0; overflow:hidden;}
.w-slide{vertical-align:top; white-space:normal; text-align:left; width:100%; height:100%; display:inline-block; position:relative;}
.w-slider-nav{z-index:2; text-align:center; -webkit-tap-highlight-color:#0000; tap-highlight-color:#0000; height:40px; margin:auto; padding-top:10px; position:absolute; inset:auto 0 0;}
.w-slider-nav.w-round > div{border-radius:100%;}
.w-slider-nav.w-num > div{font-size:inherit; line-height:inherit; width:auto; height:auto; padding:.2em .5em;}
.w-slider-nav.w-shadow > div{box-shadow:0 0 3px #3336;}
.w-slider-nav-invert{color:#fff;}
.w-slider-nav-invert > div{background-color:#2226;}
.w-slider-nav-invert > div.w-active{background-color:#222;}
.w-slider-dot{cursor:pointer; background-color:#fff6; width:1em; height:1em; margin:0 3px .5em; transition:background-color .1s,color .1s; display:inline-block; position:relative;}
.w-slider-dot.w-active{background-color:#fff;}
.w-slider-dot:focus{outline:none; box-shadow:0 0 0 2px #fff;}
.w-slider-dot:focus.w-active{box-shadow:none;}
.w-slider-arrow-left,.w-slider-arrow-right{cursor:pointer; color:#fff; -webkit-tap-highlight-color:#0000; tap-highlight-color:#0000; -webkit-user-select:none; user-select:none; width:80px; margin:auto; font-size:40px; position:absolute; inset:0; overflow:hidden;}
.w-slider-arrow-left [class^="w-icon-"],.w-slider-arrow-right [class^="w-icon-"],.w-slider-arrow-left [class*=" w-icon-"],.w-slider-arrow-right [class*=" w-icon-"]{position:absolute;}
.w-slider-arrow-left:focus,.w-slider-arrow-right:focus{outline:0;}
.w-slider-arrow-left{z-index:3; right:auto;}
.w-slider-arrow-right{z-index:4; left:auto;}
.w-icon-slider-left,.w-icon-slider-right{width:1em; height:1em; margin:auto; inset:0;}
.w-slider-aria-label{clip:rect(0 0 0 0); border:0; width:1px; height:1px; margin:-1px; padding:0; position:absolute; overflow:hidden;}
.w-slider-force-show{display:block !important;}
.w-dropdown{text-align:left; z-index:900; margin-left:auto; margin-right:auto; display:inline-block; position:relative;}
.w-dropdown-btn,.w-dropdown-toggle,.w-dropdown-link{vertical-align:top; color:#222; text-align:left; white-space:nowrap; margin-left:auto; margin-right:auto; padding:20px; text-decoration:none; position:relative;}
.w-dropdown-toggle{-webkit-user-select:none; user-select:none; cursor:pointer; padding-right:40px; display:inline-block;}
.w-dropdown-toggle:focus{outline:0;}
.w-icon-dropdown-toggle{width:1em; height:1em; margin:auto 20px auto auto; position:absolute; top:0; bottom:0; right:0;}
.w-dropdown-list{background:#ddd; min-width:100%; display:none; position:absolute;}
.w-dropdown-list.w--open{display:block;}
.w-dropdown-link{color:#222; padding:10px 20px; display:block;}
.w-dropdown-link.w--current{color:#0082f3;}
.w-dropdown-link:focus{outline:0;}
@media screen and (max-width:767px){
  .w-nav-brand{padding-left:10px;}
}
.w-lightbox-backdrop{cursor:auto; letter-spacing:normal; text-indent:0; text-shadow:none; text-transform:none; visibility:visible; white-space:normal; word-break:normal; word-spacing:normal; word-wrap:normal; color:#fff; text-align:center; z-index:2000; opacity:0; -webkit-user-select:none; -moz-user-select:none; -webkit-tap-highlight-color:transparent; background:#000000e6; outline:0; font-family:Helvetica Neue,Helvetica,Ubuntu,Segoe UI,Verdana,sans-serif; font-size:17px; font-style:normal; font-weight:300; line-height:1.2; list-style:disc; position:fixed; inset:0; -webkit-transform:translate(0);}
.w-lightbox-backdrop,.w-lightbox-container{-webkit-overflow-scrolling:touch; height:100%; overflow:auto;}
.w-lightbox-content{height:100vh; position:relative; overflow:hidden;}
.w-lightbox-view{opacity:0; width:100vw; height:100vh; position:absolute;}
.w-lightbox-view:before{content:""; height:100vh;}
.w-lightbox-group,.w-lightbox-group .w-lightbox-view,.w-lightbox-group .w-lightbox-view:before{height:86vh;}
.w-lightbox-frame,.w-lightbox-view:before{vertical-align:middle; display:inline-block;}
.w-lightbox-figure{margin:0; position:relative;}
.w-lightbox-group .w-lightbox-figure{cursor:pointer;}
.w-lightbox-img{width:auto; max-width:none; height:auto;}
/* .w-lightbox-image{float:none; max-width:100vw; max-height:100vh; display:block;} */
/* .w-lightbox-group .w-lightbox-image{max-height:86vh;} */
.w-lightbox-caption{text-align:left; text-overflow:ellipsis; white-space:nowrap; background:#0006; padding:.5em 1em; position:absolute; bottom:0; left:0; right:0; overflow:hidden;}
.w-lightbox-embed{width:100%; height:100%; position:absolute; inset:0;}
.w-lightbox-control{cursor:pointer; background-position:center; background-repeat:no-repeat; background-size:24px; width:4em; transition:all .3s; position:absolute; top:0;}
.w-lightbox-left{background-image:url("data:image/svg+xml; base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii0yMCAwIDI0IDQwIiB3aWR0aD0iMjQiIGhlaWdodD0iNDAiPjxnIHRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0ibTAgMGg1djIzaDIzdjVoLTI4eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDN2MjNoMjN2M2gtMjZ6IiBmaWxsPSIjZmZmIi8+PC9nPjwvc3ZnPg=="); display:none; bottom:0; left:0;}
.w-lightbox-right{background-image:url("data:image/svg+xml; base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMjQgNDAiIHdpZHRoPSIyNCIgaGVpZ2h0PSI0MCI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMC0waDI4djI4aC01di0yM2gtMjN6IiBvcGFjaXR5PSIuNCIvPjxwYXRoIGQ9Im0xIDFoMjZ2MjZoLTN2LTIzaC0yM3oiIGZpbGw9IiNmZmYiLz48L2c+PC9zdmc+"); display:none; bottom:0; right:0;}
.w-lightbox-close{background-image:url("data:image/svg+xml; base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMTggMTciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxNyI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMCAwaDd2LTdoNXY3aDd2NWgtN3Y3aC01di03aC03eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDd2LTdoM3Y3aDd2M2gtN3Y3aC0zdi03aC03eiIgZmlsbD0iI2ZmZiIvPjwvZz48L3N2Zz4="); background-size:18px; height:2.6em; right:0;}
.w-lightbox-strip{white-space:nowrap; padding:0 1vh; line-height:0; position:absolute; bottom:0; left:0; right:0; overflow:auto hidden;}
.w-lightbox-item{box-sizing:content-box; cursor:pointer; width:10vh; padding:2vh 1vh; display:inline-block; -webkit-transform:translate3d(0,0,0);}
.w-lightbox-active{opacity:.3;}
.w-lightbox-thumbnail{background:#222; height:10vh; position:relative; overflow:hidden;}
.w-lightbox-thumbnail-image{position:absolute; top:0; left:0;}
.w-lightbox-thumbnail .w-lightbox-tall{width:100%; top:50%; transform:translate(0,-50%);}
.w-lightbox-thumbnail .w-lightbox-wide{height:100%; left:50%; transform:translate(-50%);}
.w-lightbox-spinner{box-sizing:border-box; border:5px solid #0006; border-radius:50%; width:40px; height:40px; margin-top:-20px; margin-left:-20px; animation:.8s linear infinite spin; position:absolute; top:50%; left:50%;}
.w-lightbox-spinner:after{content:""; border:3px solid #0000; border-bottom-color:#fff; border-radius:50%; position:absolute; inset:-4px;}
.w-lightbox-hide{display:none;}
.w-lightbox-noscroll{overflow:hidden;}
@media (min-width:768px){
  .w-lightbox-content{height:96vh; margin-top:2vh;}
  .w-lightbox-view,.w-lightbox-view:before{height:96vh;}
  .w-lightbox-group,.w-lightbox-group .w-lightbox-view,.w-lightbox-group .w-lightbox-view:before{height:84vh;}
  /* .w-lightbox-image{max-width:96vw; max-height:96vh;} */
  /* .w-lightbox-group .w-lightbox-image{max-width:82.3vw; max-height:84vh;} */
  .w-lightbox-left,.w-lightbox-right{opacity:.5; display:block;}
  .w-lightbox-close{opacity:.8;}
  .w-lightbox-control:hover{opacity:1;}
}
.w-lightbox-inactive,.w-lightbox-inactive:hover{opacity:0;}
.w-richtext:before,.w-richtext:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-richtext:after{clear:both;}
.w-richtext[contenteditable="true"]:before,.w-richtext[contenteditable="true"]:after{white-space:initial;}
.w-richtext ol,.w-richtext ul{overflow:hidden;}
.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-video div:after,.w-richtext .w-richtext-figure-selected[data-rt-type="video"] div:after,.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-image div,.w-richtext .w-richtext-figure-selected[data-rt-type="image"] div{outline:2px solid #2895f7;}
.w-richtext figure.w-richtext-figure-type-video > div:after,.w-richtext figure[data-rt-type="video"] > div:after{content:""; display:none; position:absolute; inset:0;}
.w-richtext figure{max-width:60%; position:relative;}
.w-richtext figure > div:before{cursor:default !important;}
.w-richtext figure img{width:100%;}
.w-richtext figure figcaption.w-richtext-figcaption-placeholder{opacity:.6;}
.w-richtext figure div{color:#0000; font-size:0;}
.w-richtext figure.w-richtext-figure-type-image,.w-richtext figure[data-rt-type="image"]{display:table;}
.w-richtext figure.w-richtext-figure-type-image > div,.w-richtext figure[data-rt-type="image"] > div{display:inline-block;}
.w-richtext figure.w-richtext-figure-type-image > figcaption,.w-richtext figure[data-rt-type="image"] > figcaption{caption-side:bottom; display:table-caption;}
.w-richtext figure.w-richtext-figure-type-video,.w-richtext figure[data-rt-type="video"]{width:60%; height:0;}
.w-richtext figure.w-richtext-figure-type-video iframe,.w-richtext figure[data-rt-type="video"] iframe{width:100%; height:100%; position:absolute; top:0; left:0;}
.w-richtext figure.w-richtext-figure-type-video > div,.w-richtext figure[data-rt-type="video"] > div{width:100%;}
.w-richtext figure.w-richtext-align-center{clear:both; margin-left:auto; margin-right:auto;}
.w-richtext figure.w-richtext-align-center.w-richtext-figure-type-image > div,.w-richtext figure.w-richtext-align-center[data-rt-type="image"] > div{max-width:100%;}
.w-richtext figure.w-richtext-align-normal{clear:both;}
.w-richtext figure.w-richtext-align-fullwidth{text-align:center; clear:both; width:100%; max-width:100%; margin-left:auto; margin-right:auto; display:block;}
.w-richtext figure.w-richtext-align-fullwidth > div{padding-bottom:inherit; display:inline-block;}
.w-richtext figure.w-richtext-align-fullwidth > figcaption{display:block;}
.w-richtext figure.w-richtext-align-floatleft{float:left; clear:none; margin-right:15px;}
.w-richtext figure.w-richtext-align-floatright{float:right; clear:none; margin-left:15px;}
.w-nav{z-index:1000; background:#ddd; position:relative;}
.w-nav:before,.w-nav:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-nav:after{clear:both;}
.w-nav-brand{ color:#333; text-decoration:none; position:relative;display: inline-block;}
.w-nav-link{vertical-align:top; color:#222; text-align:left; margin-left:auto; margin-right:auto; padding:20px; text-decoration:none; display:inline-block; position:relative;}
.w-nav-link.w--current{color:#0082f3;}
.w-nav-menu{float:right; position:relative;}
[data-nav-menu-open]{text-align:center; background:#c8c8c8; min-width:200px; position:absolute; top:100%; left:0; right:0; overflow:visible; display:block !important;}
.w--nav-link-open{display:block; position:relative;}
.w-nav-overlay{width:100%; display:none; position:absolute; top:100%; left:0; right:0; overflow:hidden;}
.w-nav-overlay [data-nav-menu-open]{top:0;}
.w-nav[data-animation="over-left"] .w-nav-overlay{width:auto;}
.w-nav[data-animation="over-left"] .w-nav-overlay,.w-nav[data-animation="over-left"] [data-nav-menu-open]{z-index:1; top:0; right:auto;}
.w-nav[data-animation="over-right"] .w-nav-overlay{width:auto;}
.w-nav[data-animation="over-right"] .w-nav-overlay,.w-nav[data-animation="over-right"] [data-nav-menu-open]{z-index:1; top:0; left:auto;}
.w-nav-button{float:right; cursor:pointer; -webkit-tap-highlight-color:#0000; tap-highlight-color:#0000; -webkit-user-select:none; user-select:none; padding:18px; font-size:24px; display:none; position:relative;}
.w-nav-button:focus{outline:0;}
.w-nav-button.w--open{color:#fff; background-color:#c8c8c8;}
.w-nav[data-collapse="all"] .w-nav-menu{display:none;}
.w-nav[data-collapse="all"] .w-nav-button,.w--nav-dropdown-open,.w--nav-dropdown-toggle-open{display:block;}
.w--nav-dropdown-list-open{position:static;}
@media screen and (max-width:991px){
  .w-nav[data-collapse="medium"] .w-nav-menu{display:none;}
  .w-nav[data-collapse="medium"] .w-nav-button{display:block;}
}
@media screen and (max-width:767px){
  .w-nav[data-collapse="small"] .w-nav-menu{display:none;}
  .w-nav[data-collapse="small"] .w-nav-button{display:block;}
  .w-nav-brand{padding-left:10px;}
}
@media screen and (max-width:479px){
  .w-nav[data-collapse="tiny"] .w-nav-menu{display:none;}
  .w-nav[data-collapse="tiny"] .w-nav-button{display:block;}
}
.w-tabs{position:relative;}
.w-tabs:before,.w-tabs:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-tabs:after{clear:both;}
.w-tab-menu{position:relative;}
.w-tab-link{vertical-align:top; text-align:left; cursor:pointer; color:#222; background-color:#ddd; padding:9px 30px; text-decoration:none; display:inline-block; position:relative;}
.w-tab-link.w--current{background-color:#c8c8c8;}
.w-tab-link:focus{outline:0;}
.w-tab-content{display:block; position:relative; overflow:hidden;}
.w-tab-pane{display:none; position:relative;}
.w--tab-active{display:block;}
@media screen and (max-width:479px){
  .w-tab-link{display:block;}
}
.w-ix-emptyfix:after{content:"";}
@keyframes spin{
  0%{transform:rotate(0);}
  100%{transform:rotate(360deg);}
}
.w-dyn-empty{background-color:#ddd; padding:10px;}
.w-dyn-hide,.w-dyn-bind-empty,.w-condition-invisible{display:none !important;}
.wf-layout-layout{display:grid;}
.w-code-component > *{width:100%; height:100%; position:absolute; top:0; left:0;}

/* Screen reader only class - visually hidden but accessible to screen readers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
:root{--color--white:white; --font-family--body:Poppins,sans-serif; --color--body:#2B2B2B; --heading-family:Helveticanowdisplay,Arial,sans-serif; --color--black:var(--color--black); --color--prime:#DD6826; --border-radius--md:20px; --color--accent:#F3F3EF; --border-radius:16px; --color--secondary-2:#ced7e9; --color--gary-3:#b3b5bc; --color--secondary-1:#d2dcfc; --color--black:#1C4928; --border-radius--lg:50px; --color--gray-1:#20242f; --heading-family-alt:"Instrument Serif",sans-serif; --color--primary-2:#ff7c30; --border-radius--sm:10px; --color--secondary-3:#bddbf5; --color--secondary-4:#e3f5fd; --color--transparent:transparent; --border-radius--xs:5px;}
.w-layout-blockcontainer{max-width:940px; margin-left:auto; margin-right:auto; display:block;}
.w-layout-grid{grid-row-gap:16px; grid-column-gap:16px; grid-template-rows:auto auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.w-form-formradioinput--inputType-custom{border:1px solid #ccc; border-radius:50%; width:12px; height:12px;}
.w-form-formradioinput--inputType-custom.w--redirected-focus{box-shadow:0 0 3px 1px #3898ec;}
.w-form-formradioinput--inputType-custom.w--redirected-checked{border-width:4px; border-color:#3898ec;}
.w-checkbox{margin-bottom:5px; padding-left:20px; display:block;}
.w-checkbox:before{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-checkbox:after{content:" "; clear:both; grid-area:1 / 1 / 2 / 2; display:table;}
.w-checkbox-input{float:left; margin:4px 0 0 -20px; line-height:normal;}
.w-checkbox-input--inputType-custom{border:1px solid #ccc; border-radius:2px; width:12px; height:12px;}
.w-checkbox-input--inputType-custom.w--redirected-checked{background-color:#3898ec; background-image:url("https://d3e54v103j8qbb.cloudfront.net/static/custom-checkbox-checkmark.589d534424.svg"); background-position:50%; background-repeat:no-repeat; background-size:cover; border-color:#3898ec;}
.w-checkbox-input--inputType-custom.w--redirected-focus{box-shadow:0 0 3px 1px #3898ec;}
.w-pagination-wrapper{flex-wrap:wrap; justify-content:center; display:flex;}
.w-pagination-previous{color:#333; background-color:#fafafa; border:1px solid #ccc; border-radius:2px; margin-left:10px; margin-right:10px; padding:9px 20px; font-size:14px; display:block;}
.w-pagination-previous-icon{margin-right:4px;}
.w-page-count{text-align:center; width:100%; margin-top:20px;}
.w-pagination-next{color:#333; background-color:#fafafa; border:1px solid #ccc; border-radius:2px; margin-left:10px; margin-right:10px; padding:9px 20px; font-size:14px; display:block;}
.w-pagination-next-icon{margin-left:4px;}
@media screen and (max-width:991px){
  .w-layout-blockcontainer{max-width:728px;}
}
@media screen and (max-width:767px){
  .w-layout-blockcontainer{max-width:none;}
}
body{background-color:var(--color--white); font-family:var(--font-family--body); color:var(--color--body); font-size:18px; font-weight:400; line-height:1.6em;}
h1{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:60px; font-weight:500; line-height:1.3em;}
h2{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:42px; font-weight:500; line-height:1.4em;}
h3{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:36px; font-weight:500; line-height:1.4em;}
h4{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:30px; font-weight:500; line-height:1.4em;}
h5{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:26px; font-weight:500; line-height:1.4em;}
h6{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:22px; font-weight:500; line-height:1.4em;}
p{margin-bottom:10px;}
a{color:var(--color--body); text-decoration:underline;}
ul,ol{margin-top:0; margin-bottom:10px; padding-left:0;}
li{margin-bottom:10px;}
img{max-width:100%; display:inline-block;}
label{margin-bottom:10px; font-weight:400; display:block;line-height: 1.4;}
strong{font-weight:bold;}
em{font-style:italic;}


/* My Styles */

blockquote {
    border: 1px solid var(--color--prime);
    border-radius: 10px;
    background-color: var(--color--accent);
    color: var(--color--black);
    text-align: center;
    margin: 0;
    padding: 16px;
    font-size: 17px;
    line-height: 28px;
}

.richText blockquote p{margin: 0;}
figure{margin-bottom:10px;}
figcaption{text-align:center; margin-top:15px;}
.bg-prime{background-color:var(--color--prime);}
.text-prime{color:var(--color--prime);}
.bg-accent{background-color:var(--color--accent);}
.bg-sec{background-color:var(--color--black);}

.pb-0{padding-bottom:0 !important;}
.margin-bottom-80{margin-bottom:80px;}
.text-right{text-align:right;}
.text-underline{text-decoration:underline;}
.margin-bottom-8{margin-bottom:8px;}
.margin-bottom-48{margin-bottom:48px;}
.container-medium{max-width:1220px; margin-left:auto; margin-right:auto; padding-left:30px; padding-right:30px;}
.heading-badge{color:var(--color--white); background-color:#be4aa5; border-radius:3px; padding:0 6px; font-size:12px;}
.style-guide-alignment{width:100%;}
.style-guide-wrapper{margin-bottom:60px;}
.style-guide-wrapper.last{margin-bottom:0;}
.rich-text ul,.rich-text ol,.rich-text p{margin-bottom:40px;}
.rich-text img{border-radius:var(--border-radius);}
.rich-text figure,.rich-text blockquote{margin-bottom:40px;}
.margin-bottom-28{margin-bottom:28px;}
.styled-guide-heading{border-bottom:1px solid var(--color--secondary-2); flex-direction:column; align-items:flex-start; margin-bottom:20px; padding-bottom:20px;}
.styled-guide-heading.last{border-bottom-width:0; margin-bottom:0; padding-bottom:0;}
.checkbox-label{padding-left:6px;}
.margin-bottom-24{margin-bottom:24px;}
.text-small{font-size:16px; line-height:1.4em;}
.text-center{text-align:center;}
.radio-button{border:1px solid var(--color--gary-3); width:auto; min-width:20px; height:auto; min-height:20px; margin-top:0; transition:border .3s ease-in-out;}
.radio-button.w--redirected-checked{border-width:5px; border-color:var(--color--prime); background-color:var(--color--white);}
.radio-button.w--redirected-focus{border-width:5px; border-color:var(--color--prime); background-color:var(--color--white); box-shadow:none;}
.heading-h3{font-family:var(--heading-family); color:var(--color--black); margin-bottom:10px; font-size:42px; font-weight:500; line-height:1.3em;}
.style-guide-header{border-radius:var(--border-radius--md); background-color:var(--color--secondary-1); color:var(--color--black); margin-bottom:30px; padding:20px;}
.heading-h5{font-family:var(--heading-family); color:var(--color--black); margin-bottom:10px; font-size:28px; font-weight:500; line-height:1.3em;}
.margin-bottom-60{margin-bottom:60px;}
.guide-item{text-align:center;}
.form-input {
    border: 1px solid var(--color--secondary-2);
    border-radius: var(--border-radius--sm);
    background-color: var(--color--accent);
    color: var(--color--black);
    height: 48px;
    margin-bottom: 15px;
    padding: 10px 14px;
    font-size: 18px;
    font-weight: 400;
    line-height: 1.4em;
    transition: all .3s ease-in-out;
}
.form-input:focus{border-color:var(--color--prime); outline: none; box-shadow: none;}
.form-input::placeholder{color:#777; font-size:16px; font-weight:400 !important; line-height:1.4em;}
.form-input.form-textarea{height:150px;}
.form-input.form-input-footer{border-color:var(--color--gray-1); background-color:var(--color--gray-1); color:var(--color--white); margin-bottom:0; padding-right:60px;}
.form-input.form-input-footer:focus{border-color:var(--color--prime);}
.form-input.form-input-footer::placeholder{color:var(--color--gary-3);}
.form-input.form-input-dark{border-color:var(--color--black); background-color:var(--color--gray-1); color:var(--color--white);}
.form-input.form-input-dark:focus{border-color:var(--color--prime);}
.form-input.form-input-dark::placeholder{color:var(--color--white);}
.no-margin{margin-bottom:0;}
.form-select{border:1px solid var(--color--secondary-2); border-radius:var(--border-radius--lg); background-color:var(--color--accent); color:var(--color--body); height:54px; margin-bottom:15px; padding:10px 20px; font-size:18px; font-weight:400; line-height:1.4em; transition:all .3s ease-in-out;}
.form-select:focus{border-color:var(--color--black); color:var(--color--black);}
.form-select::placeholder{color:var(--color--body) ; font-size:16px;}
.text-delete{text-decoration:line-through;}
.style-guide-box{border:1px solid var(--color--secondary-2); border-radius:var(--border-radius--md); padding:10px 10px 20px;}
.styled-guide-heading-wrap{flex-direction:column; width:100%; display:flex;}
.margin-bottom-70{margin-bottom:70px;}
.heading-h6{font-family:var(--heading-family); color:var(--color--black); margin-bottom:10px; font-size:24px; font-weight:500; line-height:1.3em;}
.margin-bottom-12{margin-bottom:12px;}
.margin-bottom-4{margin-bottom:4px;}
.margin-bottom-16{margin-bottom:16px;}
.styled-guide-heading-badge{grid-column-gap:15px; grid-row-gap:10px; flex-wrap:wrap; align-items:center; margin-bottom:10px; display:flex;}
.styled-guide-heading-badge.center{justify-content:center;}
.styled-guide-heading-badge.right{justify-content:flex-end;}
.style-guide-content{grid-column-gap:20px; grid-row-gap:20px; flex-wrap:wrap; grid-template-rows:auto auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; padding-left:10px; padding-right:10px; display:flex;}
.style-guide-content.form{display:block;}
.style-guide-content.list-style{grid-column-gap:20px;}
.color-box{border:1px solid var(--color--secondary-2); border-radius:var(--border-radius); width:150px; height:60px;}
.style-guide-section{padding-top:100px; padding-bottom:100px;}
.container-box{border-radius:var(--border-radius--md); background-color:var(--color--accent); text-align:center; padding:15px 20px;}
.margin-bottom-44{margin-bottom:44px;}
.text-lead{font-size:20px; line-height:1.5em;}
.heading-h1{font-family:var(--heading-family); color:var(--color--black); margin-bottom:10px; font-size:64px; font-weight:500; line-height:1.2em;}
.checkbox-field{align-items:flex-start; margin-bottom:20px; display:flex;}
.success-message{border-radius:var(--border-radius); color:var(--color--white); text-align:center; background-color:#006433; padding:15px;}
.radio-button-label{padding-left:6px;}
.text-left{text-align:left;}
.button-black{grid-column-gap:10px; grid-row-gap:10px; border-radius:var(--border-radius--lg); background-color:var(--color--black); color:var(--color--white); text-align:center; border-style:solid; border-width:0; padding:14px 40px; font-size:18px; font-weight:500; line-height:1.4em; text-decoration:none; transition:all .3s ease-in-out;}
.button-black:hover{background-color:var(--color--prime); color:var(--color--white); transform:scale(.95);}
.button-black.nav-button{padding-left:30px; padding-right:30px;}
.heading-serif{font-family:var(--heading-family-alt); font-style:italic; font-weight:400;}
.margin-bottom-36{margin-bottom:36px;}
.style-guide-spacing-box{border-radius:var(--border-radius--md); background-color:var(--color--accent); text-align:center;}
.margin-bottom-40{margin-bottom:40px;}
.checkbox{border:1px solid var(--color--gary-3); border-radius:3px; width:auto; min-width:20px; height:auto; min-height:20px; margin-top:3px;}
.checkbox.w--redirected-checked{border-color:var(--color--gary-3); background-color:var(--color--prime); background-image:url("https://cdn.prod.website-files.com/6777c6ca4cd4fd1a5c59b396/6778b500ddcd251706da36db_checkmark.svg"); background-position:50%; background-size:12px 12px; border-radius:3px;}
.checkbox.w--redirected-focus{border-color:var(--color--gary-3); box-shadow:none; border-radius:3px; transition:background-color .3s ease-in-out;}
.radio-button-field{justify-content:flex-start; align-items:center; display:flex;}
.input-group{align-items:center; margin-bottom:20px;}
.error-message{border-radius:var(--border-radius); color:var(--color--white); text-align:center; background-color:#e92222; padding:15px 20px;}
.margin-bottom-32{margin-bottom:32px;}
.heading-h2{font-family:var(--heading-family); color:var(--color--black); margin-bottom:10px; font-size:48px; font-weight:500; line-height:1.3em;}
.heading-class-badge{color:var(--color--white); background-color:#0073e6; border-radius:3px; padding:0 6px; font-size:12px;}
.color-box-name{margin-top:10px; margin-bottom:0; font-weight:400;}
.heading-h4{font-family:var(--heading-family); color:var(--color--black); margin-bottom:10px; font-size:34px; font-weight:500; line-height:1.3em;}
.margin-bottom-54{margin-bottom:54px;}
.text-mark{background-color:var(--color--prime); color:var(--color--white); padding-left:3px; padding-right:3px;}
.margin-class-box{border-radius:var(--border-radius--md); background-color:var(--color--accent); width:100%; display:flex;}
.text-bold{font-weight:600;}
.margin-bottom-20{margin-bottom:20px;}
.more-templates{z-index:1000; grid-column-gap:6px; grid-row-gap:6px; color:#000; background-color:#fff; border:1px solid #0000001a; border-radius:30px; justify-content:flex-start; align-items:center; padding:1px 10px 1px 2px; font-family:Arial,Helvetica Neue,Helvetica,sans-serif; font-size:12px; font-weight:700; text-decoration:none; display:flex; position:fixed; inset:auto 30px 30px auto;}
.bg-prime{background-color:var(--color--prime);}
.bg-primary-2{background-color:var(--color--primary-2);}
.bg-black{background-color:var(--color--black);}
.bg-white{background-color:var(--color--white);}
.container{max-width:1460px; margin-left:auto; margin-right:auto; padding-left:30px; padding-right:30px;}
.container-small{max-width:1020px; margin-left:auto; margin-right:auto; padding-left:30px; padding-right:30px;}
.inner-container{max-width:820px; margin-left:auto; margin-right:auto; padding-left:30px; padding-right:30px;}
.inner-container.left{margin-left:0;}
.inner-container-small{max-width:600px; margin-left:auto; margin-right:auto; padding-left:30px; padding-right:30px;}
.section-spacing{padding-top:100px; padding-bottom:100px;}
.section-spacing-top{padding-top:100px;}
.section-spacing-bottom{padding-bottom:100px;}
.hero-inner{padding-top:30px;}
.hero-inner-content{border-radius:var(--border-radius--md); background-color:var(--color--black); color:var(--color--gary-3); text-align:center; padding:50px;}
.hero-inner-title{color:var(--color--white); font-size:60px;}
.brand-wrap{padding-left:0;}
.nav-right{z-index:1; grid-column-gap:20px; grid-row-gap:20px; justify-content:center; align-items:center; display:flex;}
.navbar{background-color:#0000; padding-top:30px; padding-bottom:30px;}
.grid-footer-link-wrap{grid-column-gap:10px; grid-row-gap:10px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.grid-footer-link-inner{grid-column-gap:15px; grid-row-gap:15px; flex-direction:column; align-items:flex-start; display:flex;}
.coming-soon-icon{height:150px; margin-bottom:20px;}
.utility-page-coming-soon{border-radius:var(--border-radius); text-align:center; flex-direction:column; width:800px; padding:60px; display:flex;}
.coming-soon-form{align-items:flex-start; max-width:80%; margin-top:30px; margin-left:auto; margin-right:auto;}
.social-wrap{grid-column-gap:20px; grid-row-gap:20px; flex-wrap:wrap; justify-content:center; align-items:center; margin-top:30px; display:flex;}
.utility-page-wrap{justify-content:center; align-items:center; width:100vw; max-width:100%; height:100vh; max-height:100%; display:flex;}
.subscribe-form{flex-direction:column; align-items:center; display:flex;}
.link-in-bio-button-wrap{grid-column-gap:15px; grid-row-gap:15px; flex-direction:column; padding-top:30px; padding-bottom:10px; display:flex;}
.link-in-bio-icon{height:60px;}
.link-in-bio-icon-wrap{justify-content:center; align-items:center; margin-bottom:30px; margin-left:auto; margin-right:auto; text-decoration:none; display:flex;}
.link-in-bio{border-radius:var(--border-radius); background-color:var(--color--black); text-align:center; flex-direction:column; width:650px; padding:60px; display:flex;}
.link-in-bio-wrap{justify-content:center; align-items:center; min-height:100vh; padding-top:50px; padding-bottom:50px; display:flex;}
.bg-gray-1{background-color:var(--color--gray-1);}
.bg-gray-2{background-color:var(--color--body);}
.utility-page-content{border-radius:var(--border-radius); background-color:var(--color--prime); text-align:center; flex-direction:column; width:650px; margin-bottom:0; padding:60px; display:flex;}
.error-title{color:var(--color--prime); font-size:110px; line-height:1em;}
.error-description{margin-bottom:30px;}
.utility-page-form{flex-direction:column; align-items:stretch; display:flex;}
.blog-rich-text ul,.blog-rich-text ol{margin-bottom:40px; padding-left:80px; padding-right:50px;}
.blog-rich-text p{margin-bottom:40px; padding-left:50px; padding-right:50px;}
.blog-rich-text img{border-radius:var(--border-radius);}
.blog-rich-text figure{border-radius:var(--border-radius); margin-bottom:40px; overflow:hidden;}
.blog-rich-text blockquote{margin-bottom:40px;}
.blog-rich-text h4,.blog-rich-text h3,.blog-rich-text h2,.blog-rich-text h1,.blog-rich-text h5,.blog-rich-text h6{padding-left:50px; padding-right:50px;}
.brand{width:100%; height:80px;}
.grid-navbar{grid-column-gap:20px; grid-row-gap:20px; grid-template-rows:auto; grid-template-columns:1fr auto 1fr; align-items:center;}
.grid-footer{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:.7fr 1fr;}
.footer-item-title{color:var(--color--white); margin-bottom:0; font-size:24px;}
.contact-wrap{grid-column-gap:20px; grid-row-gap:10px; flex-flow:wrap; justify-content:flex-start; align-items:center; display:flex;}
.copyright-link{opacity:1; color:var(--color--white); text-decoration:none; transition:color .3s ease-in-out;}
.copyright-link:hover{color:var(--color--gary-3);}
.footer-link-item-wrap{grid-column-gap:20px; grid-row-gap:20px; flex-direction:column; display:flex;}
.footer-simple-link{opacity:.8; color:var(--color--gary-3); font-size:18px; font-weight:400; line-height:1.5em; text-decoration:none; transition:all .3s ease-in-out;}
.footer-simple-link:hover,.footer-simple-link.w--current{opacity:1; color:var(--color--white);}
.footer-logo-wrap{align-self:flex-start;}
.footer-logo{width:100%; height:50px;}
.dropdown-list-inner{border:1px solid var(--color--gary-3); border-radius:var(--border-radius--md); background-color:var(--color--accent); flex-direction:column; padding:10px 20px;}
.dropdown{z-index:1;}
.nav-link{color:var(--color--black); text-align:justify; margin:5px 20px; padding:0; font-size:18px; font-weight:500; line-height:1.2em; transition:color .3s ease-in-out;}
.nav-link:hover,.nav-link.w--current{color:var(--color--prime);}
.dropdown-link{color:var(--color--black); width:100%; margin-top:10px; margin-bottom:10px; padding:0; font-size:18px; font-weight:500; line-height:1.5em; transition:color .3s ease-in-out;}
.dropdown-link:hover,.dropdown-link.w--current{color:var(--color--prime);}
.dropdown-list{opacity:0; background-color:#0000; border-style:solid; border-width:0; border-radius:0; flex-direction:column; width:260px; padding-top:15px; display:none;}
.dropdown-icon{margin-right:0; font-size:16px; line-height:1.1em;}
.dropdown-toggle{padding:0 20px 0 0;}
.bg-gray-3{background-color:var(--color--gary-3);}
.bg-accent{background-color:var(--color--accent);}
.bg-secondary-1{background-color:var(--color--secondary-1);}
.bg-secondary-2{background-color:var(--color--secondary-2);}
.bg-secondary-3{background-color:var(--color--secondary-3);}
.bg-secondary-4{background-color:var(--color--secondary-4);}
.gradient-1{background-image:linear-gradient(180deg,var(--color--primary-2),var(--color--prime));}
.gradient-2{background-image:linear-gradient(180deg,var(--color--secondary-1),var(--color--secondary-3) 49%,var(--color--secondary-4));}
.button-gradient{border-radius:var(--border-radius--lg); background-color:var(--color--prime); background-image:linear-gradient(180deg,var(--color--primary-2),var(--color--transparent)); color:var(--color--white); text-align:center; border-style:solid; border-width:0; padding:14px 40px; font-size:18px; font-weight:500; line-height:1.4em; transition:all .3s ease-in-out;}
.button-gradient:hover{background-color:var(--color--primary-2); color:var(--color--white); transform:scale(.95);}
.button-gradient.button-full{flex:none;}
.footer-subscribe-title{color:var(--color--white); margin-bottom:20px; font-size:24px;}
.footer-form-button{background-color:var(--color--transparent); background-image:url("https://cdn.prod.website-files.com/6777c6ca4cd4fd1a5c59b396/6778c2f044158030c310b22c_icon-01.svg"); background-position:50%; background-repeat:no-repeat; background-size:contain; width:30px; height:30px; margin-top:auto; margin-bottom:auto; margin-right:18px; padding:0; position:absolute; inset:0% 0% 0% auto;}
.footer-form{position:relative;}
.grid-footer-link{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.footer-logo-item{grid-column-gap:30px; grid-row-gap:30px; flex-flow:column; justify-content:space-between; display:flex;}
.grid-footer-contact{grid-column-gap:50px; grid-row-gap:50px; border-bottom:1px solid var(--color--gray-1); grid-template-rows:auto; grid-template-columns:.7fr 1fr; margin-top:50px; padding-bottom:50px;}
.contact-link{opacity:.8; color:var(--color--gary-3); font-size:18px; font-weight:400; line-height:1.4em; text-decoration:none; transition:all .3s ease-in-out;}
.contact-link:hover{opacity:1; color:var(--color--white);}
.footer-address-list{grid-column-gap:6px; grid-row-gap:6px; color:var(--color--gary-3); flex-flow:column; display:flex;}
.text-white{color:var(--color--white);}
.footer-address-icon{flex:none; width:18px; height:18px; margin-top:5px;}
.footer-address-wrap{grid-column-gap:15px; grid-row-gap:15px; justify-content:flex-start; align-items:flex-start; display:flex;}
.footer-copyright{color:var(--color--gary-3); margin-bottom:0;}
.footer-bottom{grid-column-gap:50px; grid-row-gap:15px; flex-flow:wrap; justify-content:space-between; align-items:center; margin-top:50px; display:flex;}
.social-list{grid-column-gap:15px; grid-row-gap:15px; display:flex;}
.social-link{border:1px solid var(--color--black); border-radius:var(--border-radius--xs); background-color:var(--color--gray-1); justify-content:center; align-items:center; width:30px; height:30px; text-decoration:none; transition:background-color .3s ease-in-out; display:flex;}
.social-link:hover{background-color:var(--color--prime);}
.social-icon{width:14px; height:14px;}
.hero-section{margin-top:-140px; padding-top:200px; position:relative; overflow:hidden;}
.hero-content-center{z-index:2; text-align:center; width:90%; margin-left:auto; margin-right:auto; position:relative;}
.hero-title-center{margin-bottom:30px; font-size:74px;}
.hero-button-list{grid-column-gap:30px; grid-row-gap:15px; flex-flow:wrap; justify-content:center; align-items:center; margin-top:40px; display:flex;}
.button-icon{flex:none; width:24px; height:24px;}
.button-secondary-2-outline{grid-column-gap:10px; grid-row-gap:10px; border:1px solid var(--color--secondary-2); border-radius:var(--border-radius--lg); background-color:var(--color--white); color:var(--color--black); justify-content:center; align-items:center; padding:14px 40px; font-size:18px; font-weight:500; text-decoration:none; transition:all .3s ease-in-out; display:flex;}
.button-secondary-2-outline:hover{background-color:var(--color--accent); transform:scale(.95);}
.hero-image-wrap{z-index:2; border-radius:var(--border-radius); justify-content:center; align-items:center; max-height:768px; margin-top:120px; margin-left:30px; margin-right:30px; display:flex; position:relative; overflow:hidden;}
.hero-image{object-fit:cover; width:100%; height:100%; min-height:750px;}
.hero-blur{filter:blur(50px); width:120%; height:700px; margin-left:-10%; position:absolute; inset:-100px 0% auto;}
.hero-blur-divider{z-index:1; background-color:var(--color--white); filter:blur(50px); border-radius:100%; width:120%; height:550px; margin-left:-10%; position:absolute; inset:470px 0% auto;}
.about-section{padding-top:100px; position:relative; overflow:hidden;}
.about-content-right{z-index:2; width:75%; margin-left:auto; position:relative;}
.about-title-right{margin-bottom:30px; font-size:26px;}
.grid-about-counter{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; margin-top:50px; display:grid;}
.about-counter-title{margin-bottom:15px; font-size:48px; line-height:1em;}
.text-primary-1{color:var(--color--prime);}
.vector-01{width:450px; height:450px; margin-top:auto; margin-bottom:auto; position:absolute; inset:0% auto 0% -220px;}
.vector-01.sm{left:-320px;}
.immigration-collection-list-wrapper{height:100%;}
.immigration-slider-arrow-wrap{justify-content:center; align-items:center; width:38px; height:14px; transition:opacity .3s ease-in-out; display:flex; inset:25px 0% auto auto;}
.immigration-slider-arrow-wrap:hover{opacity:.5;}
.immigration-slider-arrow-wrap.left{right:65px;}
.immigration-mask{width:36%; max-width:36%; height:100%; overflow:visible;}
.section-title{margin-bottom:50px;}
.section-title.immigration-section-title{width:85%;}
.section-title.section-title-inline{grid-column-gap:15px; grid-row-gap:15px; flex-flow:wrap; justify-content:space-between; align-items:center; display:flex;}
.section-title.visa-type-section-title{grid-column-gap:30px; grid-row-gap:30px; flex-flow:column; justify-content:flex-start; align-items:flex-start; margin-bottom:0; display:flex;}
.section-title.case-study-section-title{padding-right:150px;}
.immigration-collection-list{height:100%;}
.immigration-slider-wrap{width:100%; display:flex;}
.immigration-slide{height:100%; margin-right:30px;}
.immigration-wrap{position:relative;}
.immigration-slider{background-color:#0000; flex:none; width:100%; height:100%; position:static;}
.immigration-collection-item{height:100%;}
.immigration-slider-arrow{width:100%; height:100%;}
.immigration-slider-arrow.right{transform:rotate(180deg);}
.empty-state{border:1px solid var(--color--prime); border-radius:var(--border-radius--md); background-color:var(--color--accent); color:var(--color--black); text-align:center; padding:10px; font-weight:500;}
.immigration-slider-wrapper{width:100%; display:flex;}
.immigration-section{position:relative; overflow:hidden;}
.hide{display:none;}
.text-italic{font-style:italic;}
.immigration-item{border-radius:var(--border-radius); background-color:var(--color--accent); flex-flow:column; height:100%; text-decoration:none; display:flex; overflow:hidden;}
.immigration-image-wrap{position:relative; overflow:hidden;}
.immigration-image{object-fit:cover; width:100%; height:100%;}
.immigration-content{flex:1; padding:20px;}
.immigration-title{margin-bottom:15px; font-size:24px;}
.immigration-arrow-wrap{z-index:2; background-color:var(--color--black); border-radius:50%; justify-content:center; align-items:center; width:60px; height:60px; display:flex; position:absolute; inset:30px 30px auto auto;}
.immigration-arrow{width:16px; height:16px;}
.step-section{position:relative; overflow:hidden;}
.grid-step{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.step-item{z-index:2; text-align:center; flex-flow:column; justify-content:flex-start; align-items:center; display:flex; position:relative;}
.step-item.center{margin-top:30px;}
.step-item.first{margin-top:0;}
.step-number{font-family:var(--heading-family); color:var(--color--white); border-radius:50%; justify-content:center; align-items:center; width:70px; height:70px; margin-bottom:20px; font-size:28px; font-weight:500; line-height:1.4em; display:flex;}
.step-title{margin-bottom:15px; font-size:28px; line-height:1.1em;}
.vector-02{width:100%; height:auto; position:absolute; top:-10px; right:30px;}
.step-wrap{position:relative;}
.countries-wrap{grid-column-gap:0; grid-row-gap:0; border-radius:var(--border-radius); background-color:var(--color--black); grid-template-rows:auto; grid-template-columns:.75fr 1fr; grid-auto-columns:1fr; padding-top:50px; padding-bottom:50px; padding-left:50px; display:grid; position:relative; overflow:hidden;}
.countries-section-title{color:var(--color--white); margin-bottom:30px;}
.countries-counter-title{color:var(--color--white); margin-bottom:0; font-size:50px; line-height:1em;}
.countries-counter-item{grid-column-gap:20px; grid-row-gap:20px; color:var(--color--accent); justify-content:flex-start; align-items:center; width:80%; margin-top:70px; display:flex;}
.countries-content-wrap{z-index:2; position:relative;}
.country-list-wrap{z-index:1; grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; width:50%; display:grid; position:absolute; inset:0% 0% 0% auto;}
.country-list{grid-column-gap:30px; grid-row-gap:30px; flex-flow:column; display:flex;}
.country-item-sm{grid-column-gap:15px; grid-row-gap:15px; border-radius:var(--border-radius--sm); background-color:var(--color--gray-1); color:var(--color--white); text-align:center; flex-flow:column; justify-content:flex-start; align-items:center; padding:15px; font-size:18px; font-weight:400; line-height:1.4em; display:flex;}
.country-flag-sm{object-fit:cover; border-radius:50%; width:44px; height:44px;}
.vector-03{opacity:.02; object-fit:cover; width:100%; height:100%; position:absolute; inset:0%;}
.case-study-slider-wrapper,.case-study-slider-wrap{width:100%; display:flex;}
.case-study-slider{background-color:#0000; flex:none; width:100%; height:100%; position:relative;}
.case-study-mask{height:100%;}
.case-study-slide{height:100%; margin-right:50px;}
.case-study-collection-list-wrapper,.case-study-collection-list,.case-study-collection-item{height:100%;}
.case-study-slider-item{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; height:100%; display:grid;}
.case-study-slider-image-wrap{border-radius:var(--border-radius); position:relative; overflow:hidden;}
.case-study-slider-image{object-fit:cover; width:100%; height:100%;}
.case-study-slider-content{grid-column-gap:20px; grid-row-gap:20px; flex-flow:column; justify-content:flex-start; align-items:flex-start; display:flex;}
.case-study-slider-title{margin-bottom:0; font-size:28px;}
.case-study-slider-description{margin-bottom:auto;}
.case-study-slider-button-wrap{width:100%; padding-top:15px; padding-right:120px;}
.case-study-slider-arrow-wrap{z-index:2; justify-content:center; align-items:center; width:38px; height:14px; transition:opacity .3s ease-in-out; display:flex; inset:auto 0% 0 auto;}
.case-study-slider-arrow-wrap:hover{opacity:.5;}
.case-study-slider-arrow-wrap.left{right:65px;}
.case-study-slider-arrow{width:100%; height:100%;}
.case-study-slider-arrow.right{transform:rotate(180deg);}
.visa-section{position:relative; overflow:hidden;}
.visa-type-section-bg{opacity:.5; width:100%; height:100%; position:absolute; inset:0%;}
.grid-visa-type{z-index:2; grid-column-gap:0; grid-row-gap:0; grid-template-rows:auto; grid-template-columns:1fr .85fr; grid-auto-columns:1fr; display:grid; position:relative;}
.grid-visa-inline{grid-column-gap:10px; grid-row-gap:20px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; align-items:start; margin-top:30px; display:grid;}
.visa-type-wrap{z-index:3; padding:30px; position:relative;}
.visa-item-inline{grid-column-gap:10px; grid-row-gap:10px; font-family:var(--heading-family); color:var(--color--black); justify-content:flex-start; align-items:flex-start; padding-bottom:1px; font-size:20px; font-weight:500; line-height:1.2em; text-decoration:none; display:flex; position:relative;}
.visa-icon-inline{flex:none; width:12px; height:12px; margin-top:8px;}
.visa-item-divider{background-color:var(--color--black); flex:1; width:100%; height:1px; position:absolute; inset:auto 0% 0%;}
.visa-type-image-wrap{margin-top:-1px; margin-bottom:-1px; margin-left:-1px;}
.visa-type-image{object-fit:cover; width:100%; height:100%;}
.vector-04{z-index:2; width:250px; margin-left:auto; margin-right:auto; position:absolute; inset:auto 0% -70px -12%;}
.accordion-wrap{border-bottom:1px solid var(--color--secondary-2); padding-top:30px; padding-bottom:30px;}
.accordion-line-vr{background-color:var(--color--prime); width:2px; height:18px; margin:auto; position:absolute;}
.accordion-line-vr.open{transform:rotate(90deg);}
.accordion-title{color:var(--color--black); align-self:center; margin-bottom:0; font-size:24px; font-weight:500; line-height:1.3em;}
.accordion-title.open{color:var(--color--prime);}
.accordion-line-wrap{flex:none; justify-content:center; align-items:center; width:18px; height:18px; margin-top:5px; display:flex; position:relative;}
.accordion-content-wrap{overflow:hidden;}
.accordion-heading{grid-column-gap:12px; grid-row-gap:12px; cursor:pointer; justify-content:space-between; align-items:flex-start; display:flex;}
.accordion-list{z-index:2; position:relative;}
.accordion-line-hr{background-color:var(--color--prime); width:18px; height:2px;}
.accordion-content{padding-top:15px;}
.blog-list{grid-column-gap:30px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.blog-item{width:100%; text-decoration:none;}
.blog-image-wrap{border-radius:var(--border-radius); width:100%; margin-bottom:15px; overflow:hidden;}
.blog-image{object-fit:cover; width:100%; height:100%;}
.blog-title{font-size:28px;}
.button-link{text-underline-position:under; font-size:18px; font-weight:500; line-height:1.4em; text-decoration:underline; transition:text-decoration-color .3s ease-in-out;}
.button-link:hover{-webkit-text-decoration-color:var(--color--transparent); text-decoration-color:var(--color--transparent);}
.hero-image-right-wrap{border-top-left-radius:var(--border-radius); border-bottom-left-radius:var(--border-radius); width:52%; position:absolute; inset:0 -1% 0 auto; overflow:hidden;}
.hero-image-right{object-fit:cover; width:100%; height:100%;}
.hero-content-left{z-index:2; width:45%; padding-top:30px; padding-bottom:30px; position:relative;}
.hero-title-left{margin-bottom:15px; font-size:60px;}
.hero-section-split{position:relative; overflow:hidden;}
.grid-hero-info{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-top:50px; display:grid;}
.hero-info-icon-wrap{background-color:var(--color--black); border-radius:50%; justify-content:center; align-items:center; width:60px; height:60px; margin-bottom:15px; display:flex;}
.hero-info-icon{width:20px; height:20px;}
.hero-info-text{color:var(--color--black); font-size:16px; font-weight:500; line-height:1.4em;}
.hero-info-text.first{margin-bottom:5px;}
.grid-immigration{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.immigration-item-light{grid-column-gap:50px; grid-row-gap:10px;  border-radius:var(--border-radius); background-color:var(--color--accent); flex-flow:column; justify-content:space-between; height:100%; padding:30px; text-decoration:none; display:flex;}
.immigration-icon{width:60px; height:60px;}
.immigration-title-dark{font-size:24px;}
.immigration-link-wrap{margin-top:15px; overflow:hidden;}
.immigration-item-content{overflow:hidden;}
.button-center{text-align:center; margin-top:50px;}
.why-choose-section-dark{margin-left:30px; margin-right:30px;}
.why-choose-wrap{border-radius:var(--border-radius); background-color:var(--color--black); padding-top:30px; padding-bottom:30px; overflow:hidden;}
.grid-why-choose{grid-column-gap:100px; grid-row-gap:100px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-bottom:80px; display:grid; position:relative;}
.why-choose-image-wrap{border-radius:var(--border-radius); overflow:hidden;}
.why-choose-image{object-fit:cover; width:100%; height:100%;}
.why-choose-content-wrap{grid-column-gap:50px; grid-row-gap:50px; flex-flow:column; justify-content:space-between; display:flex;}
.why-choose-title{color:var(--color--white);}
.why-choose-content{grid-column-gap:20px; grid-row-gap:20px; flex-flow:column; justify-content:flex-start; align-items:flex-start; display:flex;}
.text-gray-3{color:var(--color--gary-3);}
.grid-why-choose-counter{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.why-choose-counter-title{color:var(--color--white); margin-bottom:5px; font-size:48px; font-weight:500; line-height:1.1em;}
.why-choose-counter-item{color:var(--color--gary-3);}
.vector-05{z-index:2; max-width:250px; margin-left:auto; margin-right:auto; position:absolute; inset:auto 0% -30px -200px;}
.why-choose-feature-title{color:var(--color--white); margin-bottom:0; font-size:22px;}
.why-choose-feature-icon{width:10px; height:16px; margin-top:8px;}
.why-choose-feature-title-wrap{grid-column-gap:10px; grid-row-gap:10px; justify-content:flex-start; align-items:flex-start; margin-bottom:15px; display:flex;}
.why-choose-feature-item{color:var(--color--gary-3);}
.grid-visa-split{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:.5fr 1fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.visa-list{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.visa-item-split{text-decoration:none; display:block;}
.visa-split-image-wrap{border-radius:var(--border-radius--md); margin-bottom:15px; overflow:hidden;}
.visa-image{object-fit:cover; width:100%; height:100%;}
.visa-split-title{margin-bottom:0; font-size:24px; line-height:1.2em;}
.visa-split-title-wrap{grid-column-gap:15px; grid-row-gap:15px; flex-flow:wrap; justify-content:space-between; align-items:center; display:flex;}
.visa-split-icon{flex:none; width:16px; height:16px; margin-right:4px; transform:rotate(45deg);}
.cta-wrap{border-radius:var(--border-radius); position:relative; overflow:hidden;}
.cta-title{color:var(--color--white); margin-bottom:0;}
.vector-06{width:450px; height:450px; margin-top:auto; margin-bottom:auto; position:absolute; inset:0 -50px 0% auto;}
.cta-feature-list{grid-column-gap:20px; grid-row-gap:10px; flex-flow:wrap; margin-top:30px; margin-bottom:40px; display:flex;}
.cta-feature-list-item{grid-column-gap:6px; grid-row-gap:6px; color:var(--color--white); justify-content:flex-start; align-items:flex-start; display:flex;}
.cta-feature-list-icon{flex:none; margin-top:0;}
.cta-content-wrap{width:60%; padding-top:30px; padding-bottom:30px; padding-left:30px;}
.agent-list{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr 1fr 1fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.agent-item-center{text-align:center; flex-flow:column; justify-content:flex-start; align-items:center; text-decoration:none; display:flex;}
.agent-image-rounded-wrap{border-radius:50%; margin-bottom:20px; overflow:hidden;}
.agent-image{object-fit:cover; width:100%; height:100%;}
.agent-title{margin-bottom:5px; font-size:24px;}
.grid-contact-split{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.grid-contact-form-inner{grid-column-gap:30px; grid-row-gap:10px; grid-template-rows:auto auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-bottom:0; display:grid;}
.contact-split-image-wrap{border-radius:var(--border-radius); overflow:hidden;}
.contact-split-image{object-fit:cover; width:100%; height:100%;}
.testimonial-section{position:relative; overflow:hidden;}
.testimonial-marquee-wrapper{z-index:2; justify-content:flex-start; align-items:flex-start; margin-left:-50px; display:flex; position:relative;}
.testimonial-marquee-wrap{flex:none; justify-content:flex-start; align-items:flex-start; display:flex;}
.testimonial-marquee-item{border-radius:var(--border-radius); background-color:var(--color--white); width:375px; margin-right:50px; padding:30px;}
.testimonial-marquee-content{margin-bottom:30px;}
.testimonial-marquee-avatar-info{grid-column-gap:15px; grid-row-gap:15px; justify-content:flex-start; align-items:center; display:flex;}
.testimonial-marquee-avatar{object-fit:cover; border-radius:50%; width:50px; height:50px;}
.testimonial-marquee-name{margin-bottom:0; font-size:24px; line-height:1.1em;}
.testimonial-marquee-rating{width:148px; height:20px; margin-bottom:20px;}
.testimonial-section-blur{z-index:0; filter:blur(50px); width:120%; height:90%; margin-left:-10%; position:absolute; inset:auto 0% -5%;}
.testimonial-section-blur-divider{z-index:1; background-color:var(--color--white); filter:blur(50px); border-radius:100%; width:120%; height:150px; margin-left:-10%; position:absolute; inset:0% 0% auto;}
.sidebar-sticky{position:sticky; top:50px;}
.sidebar-item{border-radius:var(--border-radius); background-color:var(--color--black); color:var(--color--gary-3); padding:30px;}
.sidebar-title{color:var(--color--white); margin-bottom:30px; font-size:28px;}
.sidebar-contact{grid-column-gap:20px; grid-row-gap:20px; flex-direction:column; display:flex;}
/* .sidebar-item-inner{border-bottom:1px solid var(--color--gray-1); margin-bottom:40px; padding-bottom:50px;} */
.contact-link-wrap{grid-column-gap:12px; grid-row-gap:12px; flex-flow:wrap; justify-content:flex-start; align-items:flex-start; display:flex;}
.contact-link-icon{flex:none; width:24px; height:24px;}
.section-spacing-sm{padding-top:80px; padding-bottom:80px;}
.pagination{grid-column-gap:40px; grid-row-gap:40px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; align-items:center; margin-top:50px; display:grid;}
.page-count{margin-top:0;}
.blog-meta{grid-column-gap:30px; grid-row-gap:10px; flex-flow:wrap; justify-content:center; align-items:center; margin-bottom:20px; display:flex;}
.category-badge{border-radius:var(--border-radius); color:var(--color--white); padding:5px 20px; display:inline-block;}
.blog-detail-image-wrap{border-radius:var(--border-radius); margin-bottom:50px; overflow:hidden;}
.blog-detail-image{object-fit:cover; width:100%; height:100%;}
.about-hero-section{padding-top:50px;}
.about-hero-title{margin-bottom:0;}
.about-counter-title-sm{margin-bottom:15px; font-size:48px; line-height:1em;}
.grid-about-counter-center{grid-column-gap:30px; grid-row-gap:30px; text-align:center; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.about-feature-list{grid-column-gap:30px; grid-row-gap:10px; flex-flow:wrap; justify-content:center; align-items:center; margin-top:15px; margin-bottom:40px; display:flex;}
.about-feature-list-item{grid-column-gap:10px; grid-row-gap:10px; justify-content:flex-start; align-items:flex-start; display:flex;}
.about-feature-list-icon{flex:none; width:9px; height:14px; margin-top:5px;}
.about-hero-image{border-radius:var(--border-radius); object-fit:cover; width:100%; height:100%;}
.grid-about-hero-image{grid-column-gap:30px; grid-row-gap:30px; border-radius:var(--border-radius); grid-template-rows:auto; grid-template-columns:1fr .5fr; grid-auto-columns:1fr; margin-top:80px; margin-bottom:50px; display:grid; overflow:hidden;}
.why-choose-item{border:1px solid var(--color--accent); border-radius:var(--border-radius); background-color:var(--color--accent); flex-direction:column; gap:30px; padding:30px; display:flex;}
.grid-why-choose-two{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.why-choose-icon{width:60px; height:60px;}
.why-choose-title-two{font-size:28px;}
.contact-form-title{margin-bottom:50px;}
.contact-hero-section{margin-top:50px;}
.grid-contact{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-top:50px; margin-bottom:30px; display:grid;}
/* Override for grid-contact when mt-0-important class is applied */
.grid-contact.mt-0-important {
  margin-top: 0 !important;
}
.immigration-detail-wrap{border-radius:var(--border-radius); background-color:var(--color--black); text-align:center; background-image:url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg"); background-position:50%; background-repeat:no-repeat; background-size:cover; margin-top:10px; margin-left:30px; margin-right:30px; padding:150px 50px; position:relative; overflow:hidden;}
.immigration-detail-title{color:var(--color--white); position:relative;}
.immigration-detail-hero-content{z-index:2; position:relative;}
.immigration-detail-hero-overlay{background-color:var(--color--black); opacity:.6; width:102%; min-height:104%; margin-top:-1%; margin-left:-1%; position:absolute; inset:0%;}
.grid-immigration-detail-split{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr .6fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.grid-immigration-detail-gallery{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-bottom:30px; display:grid;}
.immigration-detail-lightbox{border-radius:var(--border-radius); cursor:zoom-in; width:100%; height:100%; overflow:hidden;}
.immigration-detail-lightbox-image{object-fit:cover; width:100%; height:100%;}
.immigration-detail-agent-wrap{border-radius:var(--border-radius); background-color:var(--color--black); flex-flow:column; padding:30px; display:flex; position:sticky; top:50px;}
.immigration-detail-agent-title{color:var(--color--white); font-size:28px;}
.immigration-detail-agent-info{grid-column-gap:15px; grid-row-gap:15px; color:var(--color--gary-3); justify-content:flex-start; align-items:center; width:100%; display:flex;}
.immigration-detail-agent-image{object-fit:cover; border-radius:50%; flex:none; width:70px; height:70px;}
.immigration-detail-agent-name{color:var(--color--white); margin-bottom:5px; font-size:20px; font-weight:500;}
.immigration-detail-sidebar-info-title{color:var(--color--white); margin-bottom:0; font-size:24px;}
.immigration-detail-sidebar-info{grid-column-gap:10px; grid-row-gap:10px; flex-direction:column; margin-top:40px; display:flex;}
.immigration-detail-sidebar-info-list{grid-column-gap:6px; grid-row-gap:6px; color:var(--color--gary-3); flex-flow:column; display:flex;}
.button-full{width:100%;}
.immigration-detail-agent-info-wrap{grid-column-gap:20px; grid-row-gap:20px; border-bottom:1px solid var(--color--gray-1); flex-flow:column; margin-top:30px; padding-bottom:30px; display:flex;}
.country-list-inline{grid-column-gap:30px; grid-row-gap:30px; flex-flow:wrap; margin-top:30px; display:flex;}
.country-item-inline{grid-column-gap:15px; grid-row-gap:15px; border-radius:var(--border-radius--sm); background-color:var(--color--accent); color:var(--color--black); text-align:center; justify-content:flex-start; align-items:center; padding:10px 15px; font-size:18px; font-weight:400; line-height:1.4em; text-decoration:none; transition:all .3s ease-in-out; display:flex;}
.country-item-inline:hover{background-color:var(--color--secondary-4); transform:translate(0,-2px);}
.visa-detail-info{grid-column-gap:50px; grid-row-gap:50px; flex-flow:column; display:flex;}
.sidebar-contact-title{color:var(--color--white); margin-bottom:0; font-size:24px;}
.grid-why-choose-split{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.why-choose-split-image-wrap{border-radius:var(--border-radius); overflow:hidden;}
.why-choose-split-image{object-fit:cover; width:100%; height:100%;}
.visa-detail-image-wrap{border-radius:var(--border-radius); overflow:hidden;}
.visa-detail-image{object-fit:cover; width:100%; height:100%;}
.country-detail-hero-image{object-fit:cover; border-radius:50%; width:100px; height:100px;}
.country-detail-hero-title{color:var(--color--white); margin-bottom:0; position:relative;}
.country-detail-hero-content{z-index:2; grid-column-gap:20px; grid-row-gap:20px; flex-flow:column; justify-content:flex-start; align-items:center; display:flex; position:relative;}
.country-detail-hero-overlay{background-color:var(--color--black); opacity:.6; width:102%; min-height:104%; margin-top:-1%; margin-left:-1%; position:absolute; inset:0%;}
.grid-countries{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.country-item{text-decoration:none;}
.country-image-wrap{border-radius:var(--border-radius); width:100%; margin-bottom:15px; position:relative; overflow:hidden;}
.country-image {
    -o-object-fit: cover;
    object-fit: cover;
    /* width: 100%; */
    /* height: 100%; */
    aspect-ratio: 10/7;
}
.country-title {
    margin-bottom: 0;
    font-size: 20px;
    color: #1c4928;
    position: absolute;
    z-index: 2;
    /* position: absolute; */
    inset: auto auto 16px 20px;
    background: #ffffff;
    padding: 6px 22px;
    border-radius: 20px;
}
.country-flag {
    z-index: 2;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    position: absolute;
    inset: 16px auto auto 20px;
}
/* .country-flag{z-index:2; object-fit:cover; border-radius:50%; width:50px; height:50px; position:absolute; inset:30px auto auto 30px;} */
.country-image-overlay{background-color:var(--color--black); opacity:0; width:102%; height:102%; margin-top:-1%; margin-left:-1%; position:absolute; inset:0%;}
.sidebar-visa-item{grid-column-gap:10px; grid-row-gap:10px; border-radius:var(--border-radius--xs); background-color:var(--color--gray-1); color:var(--color--white); padding:10px 20px; text-decoration:none; transition:all .3s ease-in-out;}
.sidebar-visa-item:hover{background-color:var(--color--prime); color:var(--color--white);}
.sidebar-visa-list{grid-column-gap:20px; grid-row-gap:20px; flex-flow:wrap; margin-top:20px; display:flex;}
.sidebar-title-inline{color:var(--color--white); margin-bottom:0; font-size:28px;}
.sidebar-title-inline-wrap{grid-column-gap:5px; grid-row-gap:5px; flex-flow:wrap; display:flex;}
.contact-info{grid-column-gap:10px; grid-row-gap:10px; flex-flow:column; justify-content:flex-start; align-items:flex-start; display:flex;}
.contact-item{grid-column-gap:50px; grid-row-gap:16px; border:1px solid var(--color--accent); border-radius:var(--border-radius); background-color:var(--color--accent); flex-flow:column; justify-content:space-between; padding:30px; display:flex;}
.contact-icon{width:50px; height:50px;}
.contact-info-title{margin-bottom:0; font-size:28px;}
.address-list{grid-column-gap:6px; grid-row-gap:6px; flex-flow:column; display:flex;}
.about-split-feature-title{color:var(--color--prime); font-size:30px;}
.grid-about-split-feature{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-top:50px; display:grid;}
.about-content-wrap{width:75%; margin-left:auto; margin-right:auto;}
.about-section-split{position:relative;}
.error-title-icon{background-image:url("https://cdn.prod.website-files.com/6777c6ca4cd4fd1a5c59b396/67879eb995173b0cbc67cee2_visahub-icon.svg"); background-position:50%; background-repeat:no-repeat; background-size:contain; width:80px; height:80px; margin-left:10px; margin-right:10px; display:inline-block;}
.grid-agent{grid-column-gap:50px; grid-row-gap:50px; flex-flow:wrap; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; justify-content:center; align-items:flex-start; display:flex;}
.agent-collection-item{width:27.5%;}
.testimonial-image-wrap{border-radius:var(--border-radius--sm); overflow:hidden;}
.testimonial-image-wrap.one{margin-bottom:20px;}
.testimonial-popup{border-radius:var(--border-radius--md); cursor:auto; width:100%; height:100%; margin-bottom:30px; position:relative; overflow:hidden;}
.testimonial-name{font-family:var(--heading-family); color:var(--color--prime); margin-bottom:5px; font-size:20px; font-weight:500; line-height:1.2em;}
.testimonial-name.three{color:var(--color--white);}
.grid-testimonial{grid-column-gap:30px; grid-row-gap:30px; column-count:3; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; column-gap:30px; margin-bottom:50px; position:relative;}
.testimonial-content{margin-bottom:20px;}
.testimonial-content.two{margin-bottom:15px;}
.testimonial-content.three{z-index:2; position:relative;}
.lightbox-link-inline{grid-column-gap:10px; grid-row-gap:10px; border-radius:var(--border-radius--sm); color:var(--color--white); justify-content:flex-start; align-items:center; padding:8px 10px; font-size:14px; font-weight:500; line-height:1.4em; text-decoration:none; display:flex; position:absolute; inset:auto auto 20px 20px;}
.lightbox-link-icon-inline{flex:none; width:18px; height:18px;}
.testimonial-image{object-fit:cover; width:100%; height:100%;}
.testimonial-wrap{grid-column-gap:10px; grid-row-gap:10px; border:1px solid var(--color--secondary-1); border-radius:var(--border-radius--md); background-color:var(--color--accent); color:var(--color--gray-1); flex-flow:column; margin-bottom:30px; padding:30px; display:flex; position:relative; overflow:hidden;}
.testimonial-wrap.three{background-color:var(--color--black); color:var(--color--white); background-image:url("https://cdn.prod.website-files.com/6777c6ca4cd4fd1a5c59b396/678886ee5c8d71d338d83574_testimonial-02.avif"); background-position:50% 0; background-repeat:no-repeat; background-size:cover; padding-top:130px;}
.testimonial-overlay{background-color:var(--color--black); opacity:.7; width:100%; height:100%; position:absolute; inset:0%;}
.testimonial-avatar-wrap{z-index:2; position:relative;}
.testimonial-rating{align-self:flex-start; width:102px; height:14px;}
.testimonial-avatar-inline{grid-column-gap:15px; grid-row-gap:15px; display:flex;}
.testimonial-avatar{border-radius:50%; flex:none; width:60px; height:60px;}
.grid-case-detail-image{z-index:2; grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr .5fr; grid-auto-columns:1fr; display:grid; position:relative;}
.case-detail-lightbox-link{border-radius:var(--border-radius); cursor:zoom-in; width:100%; height:100%; position:relative; overflow:hidden;}
.case-detail-lightbox-image{object-fit:cover; width:100%; height:100%;}
.case-detail-collection-list-wrapper{height:100%;}
.case-detail-collection-list{grid-column-gap:30px; grid-row-gap:30px; flex-flow:column; height:100%; display:flex;}
.case-detail-collection-item{height:100%;}
.grid-case-detail{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr .4fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.case-detail-sidebar-item{border-top:2px solid var(--color--gray-1); padding-top:30px;}
.case-detail-sidebar-item.first{border-top-width:0; padding-top:0;}
.case-detail-sidebar-sticky{grid-column-gap:30px; grid-row-gap:30px; border-radius:var(--border-radius); background-color:var(--color--black); flex-flow:column; padding:30px; display:flex; position:sticky; top:50px;}
.case-detail-sidebar-title{color:var(--color--gary-3); margin-bottom:3px; font-size:16px; line-height:1.3em;}
.case-detail-sidebar-info{color:var(--color--white); font-size:20px; font-weight:500; line-height:1.3em;}
.case-detail-hero-content{z-index:2; text-align:center; width:90%; margin-bottom:50px; margin-left:auto; margin-right:auto; position:relative;}
.case-detail-hero-description{color:var(--color--gray-1); width:90%; margin-bottom:0; margin-left:auto; margin-right:auto;}
.agent-detail-image-wrap{border-radius:var(--border-radius--md); width:100%; overflow:hidden;}
.agent-detail-title{color:var(--color--black); margin-bottom:2px; font-size:32px;}
.agent-detail-hero-section{margin-top:-120px; padding-top:180px; position:relative; overflow:clip;}
.agent-detail-badge{border-radius:var(--border-radius); background-color:var(--color--black); color:var(--color--white); text-align:center; justify-content:center; align-self:flex-start; align-items:center; padding:6px 20px; font-size:14px; font-weight:500; line-height:1.3em;}
.agent-detail-info-list{grid-column-gap:15px; grid-row-gap:15px; flex-flow:wrap; margin-top:20px; display:flex;}
.agent-detail-contact-link {
  color: var(--color--prime);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.2em;
  transition: all .3s ease-in-out;
  text-decoration: underline;
  width: 30px;
  height: 30px;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  border: 1px solid #f4433633;
  transition: all .2s ease-in-out;
}
.agent-detail-contact-link:hover{color:#ffffff; -webkit-text-decoration-color:var(--color--transparent); text-decoration-color:var(--color--transparent); background: var(--color--prime);
}
.agent-detail-content{z-index:2; position:relative;}
/* .agent-detail-description{margin-bottom:auto;} */
.agent-detail-content-wrap{grid-column-gap:15px; grid-row-gap:15px; flex-flow:column; display:flex;}
.grid-agent-detail{z-index:2; grid-column-gap:30px; grid-row-gap:30px; border-radius:var(--border-radius); color:var(--color--body); grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-bottom:30px; padding:30px; display:grid; position:relative; overflow:hidden;}
.case-item{text-decoration:none;}
.grid-case{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.case-study-image-wrapper{border-radius:var(--border-radius); position:relative; overflow:hidden;}
.case-study-image{object-fit:cover; width:100%; height:100%;}
.case-study-title{margin-bottom:0; font-size:28px;}
.case-study-section-light{background-color:var(--color--accent); position:relative; overflow:hidden;}
.case-study-slider-content-split{grid-column-gap:20px; grid-row-gap:20px; border-radius:var(--border-radius); background-color:var(--color--white); flex-flow:column; justify-content:flex-start; align-items:flex-start; padding:30px; display:flex;}
.case-study-slider-item-split{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; height:100%; display:grid;}
.case-study-mask-split{max-width:80%; height:100%; overflow:visible;}
.case-study-slider-arrow-wrap-split{z-index:2; justify-content:center; align-items:center; width:38px; height:14px; transition:opacity .3s ease-in-out; display:flex; inset:26px 0% auto auto;}
.case-study-slider-arrow-wrap-split:hover{opacity:.5;}
.case-study-slider-arrow-wrap-split.left{right:65px;}
.case-wrap{position:relative;}
.case-study-slider-split{background-color:#0000; flex:none; width:100%; height:100%; position:static;}
.country-collection-list-wrapper.two{margin-top:-40%;}
.grid-visa-detail,.grid-country-detail{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr .6fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.country-detail-wrap{border-radius:var(--border-radius); background-color:var(--color--black); text-align:center; background-image:url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg"); background-position:50%; background-repeat:no-repeat; background-size:cover; margin-top:10px; margin-left:30px; margin-right:30px; padding:150px 50px; position:relative; overflow:hidden;}
.vector-01-image{opacity:.3;}
.case-study-slider-lightbox-link{z-index:2; border-radius:50%; justify-content:center; align-items:center; width:60px; height:60px; padding-left:2px; transition:transform .3s ease-in-out; display:flex; position:absolute; inset:30px auto auto 30px;}
.case-study-slider-lightbox-link:hover{transform:scale(.95);}
.case-study-slider-lightbox-icon{width:24px; height:24px;}
.lightbox-link{grid-column-gap:10px; grid-row-gap:10px; border-radius:var(--border-radius--xs); color:var(--color--white); justify-content:flex-start; align-items:center; padding:10px 20px; font-size:16px; line-height:1.4em; text-decoration:none; transition:transform .3s ease-in-out; display:flex;}
.lightbox-link:hover{transform:scale(.95);}
.lightbox-link-icon{flex:none; width:20px; height:20px;}
.blog-date{color:var(--color--black); margin-bottom:8px;}
.text-black{color:var(--color--black);}
.about-content{grid-column-gap:20px; grid-row-gap:20px; flex-flow:column; display:flex;}
.case-type{margin-bottom:2px;}
.case-info{padding-top:15px; text-decoration:none; display:block;}
.case-study-image-wrap{text-decoration:none; display:block;}
@media screen and (min-width:1280px){
  .button-black.nav-button{padding-left:40px; padding-right:40px;}
  .grid-footer,.grid-footer-contact{grid-column-gap:80px; grid-row-gap:80px;}
  .hero-image-wrap{margin-left:50px; margin-right:50px;}
  .about-content-right{width:75%;}
  .about-title-right{margin-bottom:50px; font-size:34px;}
  .grid-about-counter{grid-column-gap:80px; grid-row-gap:80px; margin-top:90px;}
  .about-counter-title{font-size:54px;}
  .vector-01{width:630px; height:630px;}
  .vector-01.sm{width:350px; height:350px; left:-180px;}
  .immigration-mask{width:32%; max-width:32%;}
  .section-title.visa-type-section-title{grid-column-gap:50px; grid-row-gap:50px;}
  .immigration-slide{margin-right:50px;}
  .immigration-content{padding:30px;}
  .immigration-title{font-size:24px;}
  .grid-step{grid-column-gap:100px; grid-row-gap:100px;}
  .step-item.center{margin-top:40px;}
  .step-item.first{margin-top:0;}
  .step-number{margin-bottom:40px;}
  .vector-02{top:-10px; right:80px;}
  .countries-wrap{padding-top:120px; padding-bottom:120px; padding-left:120px;}
  .countries-counter-title{font-size:84px;}
  .country-list-wrap{grid-column-gap:50px; grid-row-gap:50px; width:45%;}
  .country-list{grid-column-gap:50px; grid-row-gap:50px;}
  .country-item-sm{border-radius:var(--border-radius--md); padding:20px;}
  .grid-visa-inline{grid-column-gap:30px; grid-row-gap:30px; margin-top:60px;}
  .visa-type-wrap{padding:50px;}
  .visa-item-inline{grid-column-gap:15px; grid-row-gap:15px; font-size:22px; line-height:1.2em;}
  .vector-04{width:280px; bottom:-80px;}
  .blog-list{grid-column-gap:50px; grid-row-gap:50px;}
  .hero-content-left{padding-top:50px; padding-bottom:50px;}
  .hero-title-left{margin-bottom:50px; font-size:72px;}
  .grid-hero-info{grid-column-gap:50px; grid-row-gap:50px; margin-top:150px;}
  .hero-info-text{font-size:18px; line-height:1.4em;}
  .grid-immigration{grid-column-gap:50px; grid-row-gap:50px;}
  .immigration-item-light{grid-column-gap:50px; grid-row-gap:10px; padding:50px;}
  .why-choose-section-dark{margin-left:50px; margin-right:50px;}
  .why-choose-wrap{padding-top:80px; padding-bottom:80px;}
  .grid-why-choose{grid-column-gap:230px; grid-row-gap:230px; margin-bottom:130px;}
  .grid-why-choose-counter{grid-column-gap:50px; grid-row-gap:50px;}
  .vector-05{max-width:300px; left:-180px;}
  .grid-why-choose-feature{grid-column-gap:50px; grid-row-gap:50px;}
  .why-choose-feature-title{font-size:24px;}
  .grid-visa-split,.visa-list{grid-column-gap:50px; grid-row-gap:50px;}
  .vector-06{width:500px; height:500px;}
  .cta-content-wrap{width:60%; padding-top:50px; padding-bottom:50px; padding-left:50px;}
  .agent-list{grid-column-gap:50px; grid-row-gap:50px;}
  .grid-contact-split{grid-column-gap:80px; grid-row-gap:80px;}
  .grid-contact-form-inner{grid-column-gap:30px;}
  .grid-about-hero-image{grid-column-gap:50px; grid-row-gap:50px;}
  .why-choose-item{grid-column-gap:30px; grid-row-gap:30px; padding:50px;}
  .grid-why-choose-two{grid-column-gap:50px; grid-row-gap:50px;}
  .grid-contact{grid-column-gap:80px; grid-row-gap:80px; margin-top:80px; margin-bottom:50px;}
  .immigration-detail-wrap{padding:200px 100px;}
  .grid-immigration-detail-split{grid-column-gap:80px; grid-row-gap:80px; grid-template-columns:1fr .5fr;}
  .grid-immigration-detail-gallery{grid-column-gap:50px; grid-row-gap:50px; margin-bottom:50px;}
  .immigration-detail-agent-wrap{padding:50px;}
  .grid-why-choose-split,.grid-countries{grid-column-gap:50px; grid-row-gap:50px;}
  .contact-item{padding:50px;}
  .grid-agent{grid-column-gap:80px; grid-row-gap:80px;}
  .agent-collection-item{width:28.5%;}
  .testimonial-content{font-size:18px; line-height:1.4em;}
  .grid-case-detail-image,.case-detail-collection-list{grid-column-gap:50px; grid-row-gap:50px;}
  .case-detail-sidebar-title{font-size:18px;}
  .case-detail-sidebar-info{font-size:22px;}
  .case-detail-hero-content,.case-detail-hero-description{width:75%;}
  .grid-case{grid-column-gap:50px; grid-row-gap:50px;}
  .grid-visa-detail,.grid-country-detail{grid-column-gap:80px; grid-row-gap:80px; grid-template-columns:1fr .5fr;}
  .country-detail-wrap{padding:200px 100px;}
}
@media screen and (min-width:1440px){
  .grid-footer,.grid-footer-contact{grid-column-gap:100px; grid-row-gap:100px;}
  .vector-01.sm{width:400px; height:400px; left:-200px;}
  .step-item.center{margin-top:55px;}
  .step-item.first{margin-top:14px;}
  .visa-type-wrap{padding:70px;}
  .vector-04{width:350px;}
  .hero-image-right-wrap{width:52%;}
  .grid-hero-info{grid-column-gap:100px; grid-row-gap:100px; margin-top:220px;}
  .why-choose-wrap{padding-top:100px; padding-bottom:100px;}
  .vector-06{width:630px; height:630px;}
  .cta-content-wrap{width:50%; padding-top:100px; padding-bottom:100px; padding-left:100px;}
  .immigration-detail-wrap,.country-detail-wrap{margin-left:50px; margin-right:50px;}
}
@media screen and (min-width:1920px){
  .grid-footer,.grid-footer-contact{grid-column-gap:200px; grid-row-gap:200px;}
  .about-content-right{width:66%;}
  .vector-01{width:830px; height:830px; left:-220px;}
  .vector-01.sm{width:550px; height:550px;}
  .step-item.center{margin-top:75px;}
  .step-item.first{margin-top:20px;}
  .grid-visa-inline{grid-column-gap:40px; grid-row-gap:40px;}
  .visa-type-wrap{padding:140px;}
  .visa-item-inline{font-size:28px;}
  .vector-04{width:440px; bottom:-150px; left:-5%;}
  .why-choose-wrap{padding-top:150px; padding-bottom:150px;}
}
@media screen and (max-width:991px){
  blockquote{padding:40px;}
  .style-guide-section{padding-top:70px; padding-bottom:70px;}
  .section-spacing{padding-top:80px; padding-bottom:80px;}
  .section-spacing-top{padding-top:80px;}
  .section-spacing-bottom{padding-bottom:80px;}
  .hero-inner-content{padding:70px 50px;}
  .hero-inner-title{font-size:44px;}
  .menu-button-icon{line-height:1em;}
  .menu-button{border:1px solid var(--color--secondary-1); border-radius:var(--border-radius); background-color:var(--color--accent); color:var(--color--black); text-align:center; justify-content:center; align-items:center; padding:14px;}
  .menu-button.w--open{border-color:var(--color--black); background-color:var(--color--prime); color:var(--color--white);}
  .navbar{padding-top:20px; padding-bottom:20px;}
  .footer{padding-top:80px; padding-bottom:30px;}
  .grid-footer-link-inner{grid-column-gap:10px; grid-row-gap:10px;}
  .utility-page-coming-soon{width:100%; margin:20px;}
  .error-title{font-size:80px;}
  .blog-rich-text ul,.blog-rich-text ol,.rich-text ul,.rich-text ol{padding-left:50px; padding-right:20px;}
  .blog-rich-text p,.blog-rich-text h4,.blog-rich-text h3,.blog-rich-text h2,.blog-rich-text h1,.blog-rich-text h5,.blog-rich-text h6,
  .rich-text p,.rich-text h4,.rich-text h3,.rich-text h2,.rich-text h1,.rich-text h5,.rich-text h6{padding-left:20px; padding-right:20px;}
  .grid-navbar{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr 1fr auto;}
  .grid-footer{grid-column-gap:30px; grid-row-gap:30px; grid-template-columns:1fr;}
  .footer-link-item-wrap{grid-column-gap:15px; grid-row-gap:15px;}
  .dropdown-list-inner{border-radius:var(--border-radius--sm);}
  .nav-menu{border:1px solid var(--color--gary-3); border-radius:var(--border-radius--sm); background-color:var(--color--accent); margin-left:30px; margin-right:30px; padding:10px 20px;}
  .dropdown{display:block;}
  .nav-link{margin:8px 0; display:block;}
  .dropdown-list{padding-top:5px;}
  .grid-footer-contact{grid-column-gap:30px; grid-row-gap:30px; grid-template-columns:1fr 1fr; margin-top:30px; padding-bottom:30px;}
  .footer-bottom{margin-top:30px;}
  .hero-section{margin-top:-100px; padding-top:160px;}
  .hero-content-center{width:100%;}
  .hero-title-center{margin-bottom:15px; font-size:60px;}
  .hero-button-list{margin-top:30px;}
  .hero-image-wrap{max-height:100%; margin-top:50px;}
  .hero-image{min-height:auto;}
  .hero-blur{height:500px;}
  .hero-blur-divider{height:350px; top:270px;}
  .about-section{padding-top:80px;}
  .about-content-right{width:100%;}
  .about-title-right{font-size:24px;}
  .vector-01{display:none;}
  .immigration-mask{width:50%; max-width:50%;}
  .section-title{margin-bottom:40px;}
  .section-title.immigration-section-title{width:75%;}
  .section-title.visa-type-section-title{grid-column-gap:15px; grid-row-gap:15px;}
  .immigration-slide{margin-right:30px;}
  .immigration-arrow-wrap{width:44px; height:44px;}
  .immigration-arrow{width:14px; height:14px;}
  .grid-step{grid-column-gap:50px; grid-row-gap:50px; grid-template-columns:1fr;}
  .step-item.center{margin-top:0;}
  .step-number{margin-bottom:10px;}
  .vector-02{display:none;}
  .countries-wrap{padding-top:30px; padding-bottom:30px; padding-left:30px;}
  .countries-counter-item{grid-column-gap:10px; grid-row-gap:10px; flex-flow:column; justify-content:flex-start; align-items:flex-start; width:100%; margin-top:30px;}
  .country-list-wrap{grid-template-columns:1fr 1fr;}
  .country-item-sm{font-size:16px; line-height:1.4em;}
  .case-study-slider{padding-bottom:40px;}
  .case-study-slide{margin-right:30px;}
  .case-study-slider-item{grid-column-gap:30px; grid-row-gap:30px;}
  .case-study-slider-button-wrap{padding-right:0;}
  .case-study-slider-arrow-wrap{inset:auto 0% 0%;}
  .case-study-slider-arrow-wrap.right{right:-50px;}
  .case-study-slider-arrow-wrap.left{left:-50px; right:0%;}
  .grid-visa-type{grid-template-columns:1fr;}
  .grid-visa-inline{grid-column-gap:30px; grid-row-gap:30px; flex-flow:wrap; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
  .vector-04{display:none;}
  .accordion-wrap{padding-top:20px; padding-bottom:20px;}
  .blog-title{font-size:24px;}
  .hero-image-right-wrap{border-radius:0; width:100%; margin-bottom:-2px; position:static;}
  .hero-content-left{width:100%;}
  .hero-title-left{font-size:60px;}
  .grid-hero-info{margin-top:30px;}
  .grid-immigration{grid-template-columns:1fr 1fr;}
  .button-center{margin-top:30px;}
  .grid-why-choose{grid-column-gap:30px; grid-row-gap:30px; grid-template-columns:1fr; margin-bottom:50px;}
  .why-choose-content-wrap{grid-column-gap:30px; grid-row-gap:30px;}
  .why-choose-title{margin-bottom:5px;}
  .why-choose-content{grid-column-gap:10px; grid-row-gap:10px;}
  .vector-05{display:none;}
  .grid-why-choose-feature,.grid-visa-split{grid-template-columns:1fr;}
  .cta-title{font-size:40px;}
  .vector-06{width:380px; height:380px;}
  .cta-feature-list{margin-top:15px; margin-bottom:20px;}
  .cta-content-wrap{width:52%;}
  .agent-list{grid-row-gap:50px; grid-template-columns:1fr 1fr 1fr;}
  .agent-image-rounded-wrap{margin-bottom:10px;}
  .grid-contact-split{grid-template-columns:1fr;}
  .testimonial-marquee-item{width:300px; margin-right:30px; padding:20px;}
  .sidebar-sticky{position:static;}
  .sidebar-contact{grid-column-gap:15px; grid-row-gap:15px;}
  .section-spacing-sm{padding-top:80px; padding-bottom:80px;}
  .about-hero-title{font-size:56px;}
  .about-counter-title-sm{font-size:40px;}
  .grid-about-hero-image{margin-top:50px; margin-bottom:50px;}
  .grid-why-choose-two{grid-template-columns:1fr 1fr;}
  .contact-form-title{margin-bottom:30px;}
  .grid-contact{grid-template-columns:1fr;}
  .immigration-detail-wrap{padding-top:80px; padding-bottom:80px;}
  .immigration-detail-title{font-size:54px;}
  .grid-immigration-detail-split{grid-template-columns:1fr;}
  .grid-immigration-detail-gallery{grid-column-gap:30px; grid-row-gap:30px;}
  .immigration-detail-agent-wrap{position:static;}
  .immigration-detail-sidebar-info{grid-column-gap:15px; grid-row-gap:15px;}
  .grid-countries{grid-template-columns:1fr 1fr;}
  .about-split-feature-title{margin-bottom:5px;}
  .grid-about-split-feature{margin-top:30px;}
  .about-content-wrap{grid-column-gap:30px; grid-row-gap:30px; width:100%;}
  .error-title-icon{width:54px; height:54px; margin-left:6px; margin-right:6px;}
  .grid-agent{grid-column-gap:40px; grid-row-gap:40px;}
  .agent-collection-item{width:47%;}
  .grid-testimonial{grid-column-gap:20px; grid-row-gap:20px; column-count:2; column-gap:30px; margin-bottom:30px;}
  .lightbox-link-inline{bottom:20px; left:20px;}
  .testimonial-wrap{padding:23px;}
  .grid-case-detail{grid-column-gap:30px; grid-row-gap:30px; grid-template-columns:1fr;}
  .case-detail-sidebar-sticky{position:static; top:0;}
  .case-detail-hero-content,.case-detail-hero-description{width:100%;}
  .agent-detail-title{font-size:26px;}
  .agent-detail-hero-section{margin-top:-100px; padding-top:130px;}
  .agent-detail-info-list{grid-row-gap:10px;}
  .grid-case{grid-template-columns:1fr 1fr;}
  .case-study-title{font-size:24px;}
  .case-study-mask-split{max-width:90%;}
  .country-collection-list-wrapper.three{display:none;}
  .grid-visa-detail,.grid-country-detail{grid-template-columns:1fr;}
  .case-study-slider-lightbox-link{width:50px; height:50px;}
  .lightbox-link{bottom:20px; left:20px;}
  .about-content{grid-column-gap:10px; grid-row-gap:10px;}
}
@media screen and (max-width:767px){
  h1{font-size:48px;}
  h2{font-size:42px;}
  h3{font-size:36px;}
  h4{font-size:30px;}
  h5{font-size:24px;}
  h6{font-size:20px;}
  blockquote{padding:30px;}
  figcaption{margin-top:10px;}
  .container-medium{padding-left:20px; padding-right:20px;}
  .rich-text ul,.rich-text ol,.blog-rich-text ul,.blog-rich-text ol,.rich-text p,.rich-text figure,.rich-text blockquote{margin-bottom:30px;}
  .heading-h3{font-size:36px;}
  .heading-h5{font-size:24px;}
  .heading-h6{font-size:20px;}
  .style-guide-section{padding-top:60px; padding-bottom:60px;}
  .heading-h1{font-size:48px;}
  .heading-h2{font-size:42px;}
  .heading-h4{font-size:30px;}
  .more-templates{bottom:10px; right:10px;}
  .container,.container-small,.inner-container,.inner-container-small{padding-left:20px; padding-right:20px;}
  .section-spacing{padding-top:70px; padding-bottom:70px;}
  .section-spacing-top{padding-top:70px;}
  .section-spacing-bottom{padding-bottom:70px;}
  .hero-inner-content{padding:50px 30px;}
  .hero-inner-title{font-size:42px;}
  .nav-right{display:none;}
  .navbar{padding-top:15px; padding-bottom:15px;}
  .footer{padding-top:60px; padding-bottom:20px;}
  .grid-footer-link-wrap{grid-template-columns:1fr;}
  .grid-footer-link-inner{grid-column-gap:20px; grid-row-gap:10px;}
  .coming-soon-icon{height:120px;}
  .utility-page-coming-soon{margin:15px; padding:40px;}
  .coming-soon-form{max-width:100%;}
  .utility-page-wrap{height:auto;}
  .link-in-bio-icon{height:50px;}
  .link-in-bio-icon-wrap{margin-bottom:15px;}
  .link-in-bio{border-radius:var(--border-radius--md); margin:20px; padding:20px;}
  .link-in-bio-wrap{min-height:auto; padding-top:0; padding-bottom:0;}
  .utility-page-content{margin:20px; padding:30px;}
  .error-title{font-size:70px;}
  .blog-rich-text ul,.blog-rich-text ol,.blog-rich-text p,.blog-rich-text figure,.blog-rich-text blockquote{margin-bottom:30px;}
  .grid-navbar{grid-template-columns:1fr .5fr;}
  .footer-logo{height:40px;}
  .nav-menu{margin-left:20px; margin-right:20px;}
  .grid-footer-link{grid-column-gap:30px; grid-row-gap:30px;}
  .footer-logo-item{grid-column-gap:20px; grid-row-gap:20px;}
  .hero-section{margin-top:-90px; padding-top:140px;}
  .hero-title-center{margin-bottom:15px; font-size:48px;}
  .hero-button-list{margin-top:20px;grid-column-gap: 20px;}
  .hero-image-wrap{margin-top:30px; margin-left:20px; margin-right:20px;}
  .hero-blur{height:400px;}
  .hero-blur-divider{height:250px; top:170px;}
  .about-section{padding-top:70px;}
  .about-title-right{margin-bottom:15px; font-size:20px;}
  .grid-about-counter{grid-column-gap:15px; grid-row-gap:15px; margin-top:30px;}
  .about-counter-item{font-size:16px; line-height:1.4em;}
  .about-counter-title{margin-bottom:5px; font-size:36px;}
  .immigration-slider-arrow-wrap{inset:auto 0% 0%;}
  .immigration-slider-arrow-wrap.right{left:50px;}
  .immigration-slider-arrow-wrap.left{right:50px;}
  .immigration-mask{width:60%; max-width:60%;}
  .section-title{margin-bottom:30px;}
  .section-title.immigration-section-title{width:100%;}
  .section-title.case-study-section-title{padding-right:0;}
  .immigration-slide{margin-right:20px;}
  .immigration-slider{padding-bottom:40px;}
  .immigration-arrow-wrap{top:20px; right:20px;}
  .step-title{margin-bottom:10px;}
  .countries-wrap{grid-column-gap:50px; grid-row-gap:50px; grid-template-columns:1fr; padding-right:30px;}
  .countries-counter-item{flex-flow:row; justify-content:flex-start; align-items:center;}
  .country-list-wrap{width:100%; position:static; top:50%;}
  .country-list.two{margin-top:0;}
  .case-study-slide{margin-right:20px;}
  .case-study-slider-item{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr;}
  .case-study-slider-image-wrap{border-radius:var(--border-radius--md);}
  .case-study-slider-content{grid-column-gap:10px; grid-row-gap:10px;}
  .case-study-slider-title{font-size:24px;}
  .case-study-slider-button-wrap{padding-top:15px;}
  .grid-visa-inline{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr 1fr;}
  .visa-type-wrap{padding:20px 20px 30px;}
  .accordion-wrap{padding-top:15px; padding-bottom:15px;}
  .accordion-title{font-size:20px;}
  .accordion-content-wrap{font-size:16px; line-height:1.4em;}
  .accordion-heading{grid-column-gap:15px; grid-row-gap:15px;}
  .blog-list{grid-template-columns:1fr;}
  .hero-content-left{margin-bottom:20px;}
  .hero-title-left{font-size:48px;}
  .grid-immigration{grid-template-columns:1fr;}
  .button-center{margin-top:20px;}
  .why-choose-section-dark{margin-left:20px; margin-right:20px;}
  .why-choose-wrap{border-radius:var(--border-radius--md); padding-top:20px; padding-bottom:20px;}
  .grid-why-choose{margin-bottom:30px;}
  .why-choose-image-wrap{border-radius:var(--border-radius--md);}
  .grid-why-choose-counter{grid-column-gap:20px; grid-row-gap:20px;}
  .why-choose-counter-title{font-size:42px;}
  .grid-why-choose-feature{grid-column-gap:20px; grid-row-gap:20px;}
  .why-choose-feature-title-wrap{margin-bottom:10px;}
  .visa-list{grid-column-gap:20px; grid-template-columns:1fr 1fr;}
  .visa-split-image-wrap{margin-bottom:10px;}
  .visa-split-title{font-size:20px;}
  .visa-split-title-wrap{grid-column-gap:10px; grid-row-gap:10px;}
  .cta-wrap{border-radius:var(--border-radius--md);}
  .cta-title{font-size:34px;}
  .vector-06{display:none;}
  .cta-content-wrap{width:100%; padding:20px;}
  .agent-list{grid-template-columns:1fr 1fr;}
  .grid-contact-form-inner{grid-column-gap:20px; grid-row-gap:5px; grid-template-columns:1fr;}
  .testimonial-marquee-item{border-radius:var(--border-radius--sm); margin-right:20px; padding:15px;}
  .testimonial-marquee-name{font-size:20px;}
  .testimonial-section-blur-divider{height:100px;}
  .sidebar-item{border-radius:var(--border-radius--md);}
  .section-spacing-sm{padding-top:70px; padding-bottom:70px;}
  .pagination{grid-column-gap:10px; grid-row-gap:10px; grid-template-columns:1fr; margin-top:30px;}
  .blog-detail-image-wrap{margin-bottom:30px;}
  .about-hero-title{font-size:44px;}
  .grid-about-counter-center{grid-template-columns:1fr 1fr;}
  .about-feature-list{margin-bottom:20px;}
  .about-hero-image{border-radius:var(--border-radius--md);}
  .grid-about-hero-image{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr .75fr;}
  .why-choose-item{grid-column-gap:30px; grid-row-gap:30px; border-radius:var(--border-radius--md); padding:20px;}
  .grid-why-choose-two{grid-template-columns:1fr;}
  .contact-form-title{margin-bottom:15px;}
  .immigration-detail-wrap{margin-left:20px; margin-right:20px; padding:50px 30px;}
  .immigration-detail-title{font-size:48px;}
  .grid-immigration-detail-gallery{grid-column-gap:20px; grid-row-gap:20px;}
  .country-list-inline{grid-column-gap:20px; grid-row-gap:20px; margin-top:20px;}
  .visa-detail-info{grid-column-gap:30px; grid-row-gap:30px;}
  .grid-why-choose-split{grid-template-columns:1fr;}
  .country-detail-hero-image{width:80px; height:80px;}
  .country-detail-hero-title{font-size:42px;}
  .country-detail-hero-content{grid-column-gap:15px; grid-row-gap:15px;}
  .country-image-wrap{border-radius:var(--border-radius--md);}
  .country-title{font-size:24px;}
  .country-flag{top:15px; left:15px;}
  .contact-item{grid-column-gap:30px; grid-row-gap:30px; border-radius:var(--border-radius--md);}
  .contact-icon{width:40px; height:40px;}
  .grid-about-split-feature,.grid-agent{grid-column-gap:30px; grid-row-gap:30px;}
  .agent-collection-item{width:46.5%;}
  .testimonial-image-wrap.one{margin-bottom:15px;}
  .grid-testimonial{column-count:1; grid-template-columns:1fr 1fr; margin-bottom:20px;}
  .lightbox-link-inline{bottom:20px; left:20px;}
  .testimonial-wrap{margin-bottom:20px; padding:20px;}
  .testimonial-wrap.three{padding-top:200px;}
  .grid-case-detail-image{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr;}
  .case-detail-lightbox-link{border-radius:var(--border-radius--md);}
  .case-detail-collection-list{grid-column-gap:20px; grid-row-gap:20px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
  .case-detail-sidebar-item{padding-top:15px;}
  .case-detail-sidebar-sticky{grid-column-gap:15px; grid-row-gap:15px; border-radius:var(--border-radius--md); padding:20px;}
  .agent-detail-hero-section{margin-top:-90px; padding-top:120px;}
  .agent-detail-badge{padding-left:14px; padding-right:14px;}
  .agent-detail-info-list{grid-column-gap:15px; margin-top:0;}
  .grid-agent-detail{border-radius:var(--border-radius--md); grid-template-columns:1fr; margin-bottom:20px; padding:20px;}
  .grid-case{grid-template-columns:1fr;}
  .case-study-image-wrapper{border-radius:var(--border-radius--md);}
  .case-study-slider-content-split{border-radius:var(--border-radius--md); flex:1; padding:20px;}
  .case-study-slider-item-split{grid-column-gap:15px; grid-row-gap:15px; flex-flow:column; grid-template-columns:1fr; display:flex;}
  .case-study-slider-arrow-wrap-split{inset:auto 0% 0%;}
  .case-study-slider-arrow-wrap-split.right{right:-50px;}
  .case-study-slider-arrow-wrap-split.left{left:-50px; right:0%;}
  .case-study-slider-split{padding-bottom:60px;}
  .country-collection-list-wrapper.two{margin-top:0%;}
  .grid-country-detail{grid-column-gap:30px; grid-row-gap:30px;}
  .country-detail-wrap{border-radius:var(--border-radius--md); margin-left:20px; margin-right:20px; padding-top:50px; padding-bottom:50px;}
  .case-study-slider-lightbox-link{width:40px; height:40px; top:20px; left:20px;}
  .case-study-slider-lightbox-icon{width:20px; height:20px;}
  .lightbox-link{bottom:20px; left:20px;}
  .case-info{padding-top:10px;}
}
@media screen and (max-width:479px){
  body{font-size:16px; line-height:1.3em;}
  h1{font-size:36px;}
  h2{font-size:30px;}
  h3{font-size:26px;}
  h4{font-size:24px;}
  h5{font-size:20px;}
  h6{font-size:18px;}
  label{margin-bottom:5px;}
  blockquote{padding:20px;}
  figcaption{margin-top:5px;}
  .container-medium{padding-left:15px; padding-right:15px;}
  .style-guide-wrapper{margin-bottom:50px;}
  .rich-text ul,.rich-text ol,.rich-text p,.rich-text figure,.rich-text blockquote{margin-bottom:20px;}
  .text-small{font-size:14px; line-height:1.4em;}
  .heading-h3{font-size:34px;}
  .heading-h5{font-size:24px;}
  .form-input,.form-input::placeholder{font-size:16px; line-height:1.4em; font-weight:400 !important; color:var(--color--gray-1) ;}
  .heading-h6{font-size:18px;}
  .style-guide-section{padding-top:40px; padding-bottom:40px;}
  .text-lead{font-size:18px; line-height:1.5em;}
  .heading-h1{font-size:40px;}
  .button-black{padding:12px 30px; font-size:16px; line-height:1.4em;}
  .input-group{flex-direction:column; align-items:flex-start; margin-bottom:10px;}
  .heading-h2{font-size:36px;}
  .heading-h4{font-size:30px;}
  .container,.container-small,.inner-container,.inner-container-small{padding-left:15px; padding-right:15px;}
  .section-spacing{padding-top:60px; padding-bottom:60px;}
  .section-spacing-top{padding-top:60px;}
  .section-spacing-bottom{padding-bottom:70px;}
  .hero-inner-content{padding:30px 20px;}
  .hero-inner-title{font-size:32px; line-height:1.2em;}
  .menu-button-icon{font-size:20px; line-height:1em;}
  .menu-button{padding:10px;}
  .navbar{padding-top:10px; padding-bottom:10px;}
  .footer{padding-top:40px; padding-bottom:15px;}
  .coming-soon-icon{height:100px;}
  .utility-page-coming-soon{padding:15px;}
  .coming-soon-form{margin-top:20px;}
  .social-wrap{grid-column-gap:15px; grid-row-gap:15px; margin-top:0;}
  .link-in-bio-button-wrap{grid-column-gap:15px; grid-row-gap:15px; padding-top:20px; padding-bottom:20px;}
  .link-in-bio-icon{height:40px;}
  .link-in-bio{margin:15px; padding:15px;}
  .utility-page-content{margin:15px; padding:20px;}
  .error-title{font-size:54px;}
  .blog-rich-text ul,.blog-rich-text ol,.blog-rich-text p,.blog-rich-text figure,.blog-rich-text blockquote,
  .rich-text ul,.rich-text ol,.rich-text p,.rich-text figure,.rich-text blockquote{margin-bottom:20px;}
  .brand{height:60px;}
  .grid-navbar{grid-column-gap:10px; grid-row-gap:10px;}
  .grid-footer{grid-template-columns:1fr;}
  .footer-item-title{font-size:20px;}
  .contact-wrap,.footer-link-item-wrap{grid-column-gap:10px; grid-row-gap:10px;}
  .footer-simple-link{font-size:16px; line-height:1.4em;}
  .footer-logo{height:34px;}
  .dropdown-list-inner{padding:5px 15px;}
  .nav-menu{margin-left:15px; margin-right:15px; padding-left:15px; padding-right:15px;}
  .nav-link,.dropdown-link{font-size:16px; line-height:1.5em;}
  .dropdown-list{padding-top:5px; padding-bottom:5px;}
  .button-gradient{padding:12px 26px; font-size:14px; line-height:1.4em;}
  .footer-subscribe-title{margin-bottom:10px; font-size:20px;}
  .grid-footer-contact{grid-template-columns:1fr; padding-bottom:15px;}
  .footer-bottom{margin-top:15px;}
  .social-list{grid-column-gap:12px; grid-row-gap:12px;}
  .hero-section{margin-top:-80px; padding-top:120px;}
  .hero-title-center{font-size:36px; line-height:1.2em; margin-bottom:10px;}
  .button-icon{width:18px; height:18px;}
  .button-secondary-2-outline{padding:12px 26px; font-size:14px; line-height:1.4em;}
  .hero-image-wrap{margin-left:15px; margin-right:15px;}
  .about-section{padding-top:60px;}
  .about-title-right{font-size:18px; line-height:1.3em;}
  .grid-about-counter{grid-column-gap:24px; grid-row-gap:24px; grid-template-columns:1fr; margin-top:24px;}
  .about-counter-title{font-size:30px;}
  .immigration-mask{width:100%; max-width:100%;}
  .section-title{margin-bottom:20px;}
  .immigration-slider-wrap{flex:none; width:100%;}
  .immigration-slider-wrapper{width:100%; position:relative;}
  .immigration-item{border-radius:var(--border-radius--md);}
  .immigration-content{padding:15px;}
  .immigration-title{margin-bottom:10px; font-size:20px;}
  .immigration-arrow-wrap{top:15px; right:15px;}
  .grid-step{grid-column-gap:30px; grid-row-gap:30px;}
  .step-number{width:50px; height:50px; margin-bottom:5px; font-size:22px; line-height:1.4em;}
  .step-title{font-size:24px;}
  .countries-wrap{grid-column-gap:30px; grid-row-gap:30px; padding:20px;}
  .countries-counter-item{font-size:14px; line-height:1.4em;}
  .country-list-wrap{grid-template-columns:1fr;}
  .country-list{grid-column-gap:15px; grid-row-gap:15px; grid-template-rows:auto auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
  .country-item-sm{font-size:14px; line-height:1.3em;}
  .country-flag-sm{width:40px; height:40px;}
  .case-study-slider-wrapper{width:100%; position:relative;}
  .case-study-slider-wrap{flex:none; width:100%;}
  .case-study-mask{width:100%; max-width:100%;}
  .case-study-slider-content{grid-column-gap:5px; grid-row-gap:5px;}
  .case-study-slider-title{font-size:20px;}
  .case-study-slider-button-wrap{padding-top:10px;}
  .grid-visa-inline{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr; margin-top:15px;}
  .visa-type-wrap{padding:25px;}
  .visa-item-inline{font-size:16px; line-height:1.2em;}
  .accordion-wrap{font-size:14px; line-height:1.5em;}
  .accordion-title{font-size:18px;}
  .accordion-heading{grid-column-gap:12px; grid-row-gap:12px;}
  .accordion-content{font-size:14px; line-height:1.4em;}
  .blog-list{grid-row-gap:40px;}
  .button-link{font-size:16px; line-height:1.3em;}
  .hero-content-left{margin-bottom:15px;}
  .hero-title-left{font-size:40px;}
  .grid-hero-info{grid-template-columns:1fr;}
  .hero-info-icon-wrap{width:50px; height:50px; margin-bottom:10px;}
  .immigration-item-light{grid-column-gap:20px; grid-row-gap:10px; padding:20px;}
  .immigration-icon{width:40px; height:40px;}
  .immigration-title-dark{font-size:20px;}
  .button-center{margin-top:15px;}
  .why-choose-section-dark{margin-left:15px; margin-right:15px;}
  .why-choose-wrap{padding-top:15px; padding-bottom:15px;}
  .why-choose-title{font-size:30px;}
  .grid-why-choose-counter{grid-template-columns:1fr;}
  .why-choose-counter-title{font-size:34px;}
  .visa-list{grid-template-columns:1fr;}
  .visa-split-image-wrap{margin-bottom:10px;}
  .cta-title{font-size:28px;}
  .cta-content-wrap{padding:15px;}
  .agent-list{grid-column-gap:20px; grid-row-gap:30px;}
  .agent-item-center{font-size:14px; line-height:1.3em;}
  .agent-title{margin-bottom:3px; font-size:18px; line-height:1.3em;}
  .sidebar-item{padding:20px;}
  .sidebar-contact{grid-column-gap:10px; grid-row-gap:10px;}
  .sidebar-item-inner{margin-bottom:20px; padding-bottom:30px;}
  .sidebar-item-inner .form-input{margin-bottom:10px;}
  .sidebar-item-inner .button-gradient{width:100%;}
  .section-spacing-sm{padding-top:60px; padding-bottom:60px;}
  .pagination{margin-top:20px;}
  .blog-detail-image-wrap{margin-bottom:20px;}
  .blog-detail-image{object-fit:cover;}
  .about-hero-title{font-size:40px;}
  .about-counter-title-sm{font-size:30px;}
  .grid-about-hero-image{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr; margin-top:30px; margin-bottom:30px;}
  .why-choose-item{padding:15px;}
  .why-choose-icon{width:50px; height:50px;}
  .why-choose-title-two{font-size:24px;}
  .immigration-detail-wrap{margin-left:15px; margin-right:15px; padding:30px 20px;}
  .immigration-detail-title{font-size:40px;}
  .grid-immigration-detail-gallery{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr;}
  .immigration-detail-agent-wrap{padding:15px;}
  .immigration-detail-agent-info{font-size:14px;}
  .immigration-detail-agent-image{width:70px; height:70px;}
  .immigration-detail-agent-name{font-size:20px;}
  .immigration-detail-sidebar-info{grid-column-gap:10px; grid-row-gap:10px;}
  .immigration-detail-agent-info-wrap{font-size:14px;}
  .sidebar-contact-title{font-size:22px;}
  .country-detail-hero-image{width:60px; height:60px;}
  .country-detail-hero-title{font-size:38px;}
  .country-detail-hero-content{grid-column-gap:5px; grid-row-gap:5px;}
  .grid-countries{grid-template-columns:1fr;}
  .country-image-wrap{margin-bottom:10px;}
  .sidebar-visa-item{padding:6px 14px; font-size:14px;}
  .sidebar-visa-list{grid-column-gap:10px; grid-row-gap:10px;}
  .sidebar-title-inline{font-size:22px;}
  .contact-item{grid-column-gap:20px; grid-row-gap:20px; padding:20px;}
  .contact-icon{width:30px; height:30px;}
  .contact-info-title{font-size:22px;}
  .about-split-feature-title{font-size:24px;}
  .grid-about-split-feature{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr;}
  .error-title-icon{width:40px; height:40px; margin-left:4px; margin-right:4px;}
  .grid-agent{grid-column-gap:15px; grid-row-gap:30px;}
  .testimonial-image-wrap.one{margin-bottom:10px;}
  .testimonial-popup{margin-bottom:15px;}
  .testimonial-name{margin-bottom:3px; font-size:18px; line-height:1.2em;}
  .grid-testimonial{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr; margin-bottom:15px;}
  .testimonial-content{font-size:14px; line-height:1.4em;}
  .lightbox-link-inline{font-size:12px; line-height:1.4em; bottom:15px; left:15px;}
  .testimonial-wrap{margin-bottom:15px; padding:12px;}
  .testimonial-wrap.three{padding-top:150px;}
  .testimonial-avatar{width:50px; height:50px;}
  .grid-case-detail-image{grid-column-gap:15px; grid-row-gap:15px;}
  .case-detail-collection-list{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr;}
  .case-detail-sidebar-item{padding-top:20px;}
  .case-detail-sidebar-sticky{grid-column-gap:20px; grid-row-gap:20px; padding:15px;}
  .agent-detail-hero-section{margin-top:-65px; padding-top:95px;}
  .agent-detail-info-list{grid-column-gap:10px; grid-row-gap:10px; margin-top:10px; margin-bottom:10px;}
  .agent-detail-content-wrap{grid-column-gap:10px; grid-row-gap:10px;}
  .grid-agent-detail{grid-column-gap:15px; grid-row-gap:15px; margin-bottom:15px; padding:15px;}
  .case-study-title{font-size:20px; line-height:1.3em;}
  .case-study-slider-content-split{grid-column-gap:10px; grid-row-gap:10px; padding:15px;}
  .case-study-mask-split{width:100%; max-width:100%;}
  .country-collection-list-wrapper.two{display:none;}
  .grid-country-detail{grid-column-gap:20px; grid-row-gap:20px;}
  .country-detail-wrap{margin-left:15px; margin-right:15px; padding:30px;}
  .case-study-slider-lightbox-link{top:15px; left:15px;}
  .lightbox-link{bottom:15px; left:15px;}
  .lightbox-link-icon{width:20px; height:20px;}
  .blog-date{margin-bottom:5px;}
  .case-type{font-size:14px; line-height:1.4em;}
}
#w-node-bd6a3f5e-7429-54a5-9294-201eb705bfa1-5c59b398{justify-self:start;}
#w-node-_7c3bf295-6060-045d-7271-bf484b242481-ecf59ae5{grid-area:span 1 / span 1 / span 1 / span 1;}
#w-node-_63067e08-366b-f93d-ca5a-9cb96c243fdd-2ccf17b1{justify-self:start;}
#w-node-e551e49f-aeab-d0a0-3375-8de75a269063-2ccf17b1{justify-self:center;}
#w-node-_63067e08-366b-f93d-ca5a-9cb96c243ffe-2ccf17b1{justify-self:end;}
#w-node-_77b51261-d3a7-97c5-39dc-7a4a47472fc7-87687d97,#w-node-_77b51261-d3a7-97c5-39dc-7a4a47472fc7-dd74d3aa{grid-area:span 1 / span 2 / span 1 / span 2;}
@media screen and (max-width:991px){
  #w-node-e551e49f-aeab-d0a0-3375-8de75a269063-2ccf17b1{justify-self:auto;}
  #w-node-_63067e08-366b-f93d-ca5a-9cb96c244005-2ccf17b1{justify-self:end;}
  #w-node-_63bdca00-d749-298a-957d-ccf9955bcc13-dd74d3aa{order:-9999;}
}
@media screen and (max-width:767px){
  #w-node-_77b51261-d3a7-97c5-39dc-7a4a47472fc7-87687d97,#w-node-_77b51261-d3a7-97c5-39dc-7a4a47472fc7-dd74d3aa{grid-column:span 1 / span 1;}
  #w-node-_9d96d2b5-858d-8256-5e36-274d54252a9b-6bc3288d{grid-area:span 1 / span 1 / span 1 / span 1;}
}
@media screen and (max-width:479px){
  #w-node-_7c3bf295-6060-045d-7271-bf484b242481-ecf59ae5,#w-node-_07b30d10-699d-b2dc-c1b2-f6cde3af0c94-ecf59ae5,#w-node-d70db279-fe22-48cf-776b-954ca665c3ed-ecf59ae5{grid-column:span 1 / span 1;}
}
@font-face{font-family:'Helveticanowdisplay'; src:url('https://cdn.prod.website-files.com/6777c6ca4cd4fd1a5c59b396/6777d78a2bab4e39e382e3dc_HelveticaNowDisplay-Medium.woff2') format('woff2'); font-weight:500; font-style:normal; font-display:swap;}


/* Taimoor Styles */
.line{
  margin: 0 0 10px 10px;
}



.richText strong {
    font-weight: 500;
    margin: 0 0 10px;
}
.richText p {
   
    margin: 0 0 40px;
}
.richText ul {
    list-style: disc;
    padding-left: 30px;
    margin: 0 0 40px;
}
.richText ul li{
    list-style: disc;
    margin: 0 0 10px;
}
.richText ul li p {
    margin: 0;
}
