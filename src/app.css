@tailwind base;

/* Base styles that should be overridable by Tailwind */
@layer base {
  /* Root variables */
  :root {
    --color--white: white;
    --font-family--body: <PERSON><PERSON><PERSON>, sans-serif;
    --color--body: #2B2B2B;
    --heading-family: Helveticanowdisplay, Arial, sans-serif;
    --color--black: #1C4928;
    --color--prime: #DD6826;
    --border-radius--md: 20px;
    --color--accent: #F3F3EF;
    --border-radius: 16px;
    --color--secondary-2: #ced7e9;
    --color--gary-3: #b3b5bc;
    --color--secondary-1: #d2dcfc;
    --border-radius--lg: 50px;
    --color--gray-1: #20242f;
    --heading-family-alt: "Instrument Serif", sans-serif;
    --color--primary-2: #ff7c30;
    --border-radius--sm: 10px;
    --color--secondary-3: #bddbf5;
    --color--secondary-4: #e3f5fd;
    --color--transparent: transparent;
    --border-radius--xs: 5px;
  }
  
  /* Base element styles */
  body {
    @apply font-sans text-[var(--color--body)] text-lg font-normal leading-relaxed;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-sans text-[var(--color--black)] font-medium mb-2.5 mt-0;
  }
}

@tailwind components;

/* Component styles */
@layer components {
  /* Country components */
  .country-item {
    @apply no-underline !important;
  }

  .country-image-wrap {
    border-radius: var(--border-radius);
    width: 100%;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
  }

  .country-image {
    object-fit: cover;
    aspect-ratio: 10/7;
    transition: transform 0.5s ease-in-out;
  }

  .country-item:hover .country-image {
    transform: scale(1.05);
  }

  .country-title {
    margin-bottom: 0;
    font-size: 20px;
    color: #1c4928;
    position: absolute;
    z-index: 2;
    inset: auto auto 16px 20px;
    background: white;
    padding: 6px 22px 6px 30px;
    border-radius: 20px;
    border: 1px solid var(--color--prime);
    color: var(--color--prime);
    transition: all 0.3s ease-in-out;
  }

  .country-item:hover .country-title {
    background-color: var(--color--prime);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .country-flag {
    z-index: 2;
    object-fit: cover;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    position: absolute;
    inset: 16px auto auto 20px;
  }

  .country-image-overlay {
    background-color: var(--color--black);
    opacity: 0;
    width: 102%;
    height: 102%;
    margin-top: -1%;
    margin-left: -1%;
    position: absolute;
    inset: 0;
    transition: opacity 0.3s ease-in-out;
  }

  .country-item:hover .country-image-overlay {
    opacity: 0.2;
  }
}

@tailwind utilities;

/* Custom utilities that should override everything else */
@layer utilities {
  .mt-0-important {
    margin-top: 0 !important;
  }
  
  .bounce {
    animation: bounce 2s infinite;
  }
}

/* Animations and other styles that don't fit in layers */
@keyframes bounce {
  0%,20%,50%,80%,100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* View Transitions API styles */
::view-transition-old(root),
::view-transition-new(root) {
  animation-duration: 0.5s;
}

/* ProseMirror styles - consider moving these to @layer components */
.ProseMirror {
  position: relative;
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
}
.ProseMirror li{position:relative;}
.ProseMirror-hideselection *::selection{background:transparent;}
.ProseMirror-hideselection *::-moz-selection{background:transparent;}
.ProseMirror-hideselection{caret-color:transparent;}
.ProseMirror-selectednode{outline:2px solid #8cf;}
/* Make sure li selections wrap around markers */
li.ProseMirror-selectednode{outline:none;}
li.ProseMirror-selectednode:after{content:""; position:absolute; left:-32px; right:-2px; top:-2px; bottom:-2px; border:2px solid #8cf; pointer-events:none;}
/* My Styles */

/* html{-webkit-text-size-adjust:100%; -ms-text-size-adjust:100%; font-family:sans-serif;}
body{margin:0;}
*/
article,figure,footer,header,menu,nav,section{display:block;}
canvas,progress{vertical-align:baseline; display:inline-block;}
[hidden],template{display:none;}
a{background-color:#0000; text-decoration:none !important;}
a:active,a:hover{outline:0;}
b,strong{font-weight:bold;}
h1{margin:.67em 0; font-size:2em;}
mark{color:#000; background:#ff0;}
img{border:0;}
svg:not(:root){overflow:hidden;}
hr{box-sizing:content-box; height:0;}
code{font-family:monospace; font-size:1em;}
button,input,select,textarea{color:inherit; font:inherit; margin:0;}
button{overflow:visible;}
button,select{text-transform:none;}
button,html input[type="button"],input[type="reset"]{-webkit-appearance:button; cursor:pointer;}
button[disabled],html input[disabled]{cursor:default;}
button::-moz-focus-inner,input::-moz-focus-inner{border:0; padding:0;}
input{line-height:normal;}
input[type="checkbox"]{box-sizing:border-box; padding:0;}
input[type="number"]::-webkit-inner-spin-button,input[type="number"]::-webkit-outer-spin-button{height:auto;}
input[type="search"]{-webkit-appearance:none;}
input[type="search"]::-webkit-search-cancel-button,input[type="search"]::-webkit-search-decoration{-webkit-appearance:none;}
textarea{overflow:auto;}
table{border-collapse:collapse; border-spacing:0;}
td,th{padding:0;}
@font-face{font-family:webflow-icons; src:url("data:application/x-font-ttf; charset=utf-8; base64,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") format("truetype"); font-weight:normal; font-style:normal;}
.w-icon-slider-right:before{content:"î˜€";}
.w-icon-slider-left:before{content:"î˜";}
.w-icon-nav-menu:before{content:"î˜‚";}
.w-icon-arrow-down:before,.w-icon-dropdown-toggle:before{content:"î˜ƒ";}
.w-icon-file-upload-remove:before{content:"î¤€";}
.w-icon-file-upload-icon:before{content:"î¤ƒ";}
*{box-sizing:border-box;}
html{height:100%;}
body{height:100%;}
img{vertical-align:middle; max-width:100%; display:inline-block;}
html.w-mod-touch *{background-attachment:scroll !important;}
.w-block{display:block;}
.w-inline-block{max-width:100%; display:inline-block;}
.w-clearfix:before,.w-clearfix:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-clearfix:after{clear:both;}
.w-hidden{display:none;}
.w-button{color:#fff; line-height:inherit; cursor:pointer; background-color:#3898ec; border:0; border-radius:0; padding:9px 15px; text-decoration:none; display:inline-block;}
input.w-button{-webkit-appearance:button;}
.w-code-block{margin:unset;}
.w-optimization{display:contents;}
.w-webflow-badge,.w-webflow-badge > img{box-sizing:unset; width:unset; height:unset; max-height:unset; max-width:unset; min-height:unset; min-width:unset; margin:unset; padding:unset; float:unset; clear:unset; border:unset; border-radius:unset; background:unset; background-image:unset; background-position:unset; background-size:unset; background-repeat:unset; background-origin:unset; background-clip:unset; background-attachment:unset; background-color:unset; box-shadow:unset; transform:unset; direction:unset; font-family:unset; font-weight:unset; color:unset; font-size:unset; line-height:unset; font-style:unset; font-variant:unset; text-align:unset; letter-spacing:unset; -webkit-text-decoration:unset; text-decoration:unset; text-indent:unset; text-transform:unset; list-style-type:unset; text-shadow:unset; vertical-align:unset; cursor:unset; white-space:unset; word-break:unset; word-spacing:unset; word-wrap:unset; transition:unset;}
.w-webflow-badge{white-space:nowrap; cursor:pointer; box-shadow:0 0 0 1px #0000001a,0 1px 3px #0000001a; visibility:visible !important; opacity:1 !important; z-index:2147483647 !important; color:#aaadb0 !important; overflow:unset !important; background-color:#fff !important; border-radius:3px !important; width:auto !important; height:auto !important; margin:0 !important; padding:6px !important; font-size:12px !important; line-height:14px !important; text-decoration:none !important; display:inline-block !important; position:fixed !important; inset:auto 12px 12px auto !important; transform:none !important;}
.w-webflow-badge > img{position:unset; visibility:unset !important; opacity:1 !important; vertical-align:middle !important; display:inline-block !important;}
h1,h2,h3,h4,h5,h6{margin-bottom:10px; font-weight:bold;}
h1{margin-top:20px; font-size:38px; line-height:44px;}
h2{margin-top:20px; font-size:32px; line-height:36px;}
h3{margin-top:20px; font-size:24px; line-height:30px;}
h4{margin-top:10px; font-size:18px; line-height:24px;}
h5{margin-top:10px; font-size:14px; line-height:20px;}
h6{margin-top:10px; font-size:12px; line-height:18px;}
p{margin-top:0; margin-bottom:10px;}
blockquote{border-left:5px solid #e2e2e2; margin:0 0 10px; padding:10px 20px; font-size:18px; line-height:22px;}
figure{margin:0 0 10px;}
ul,ol{margin-top:0; margin-bottom:10px; padding-left:40px;}
.w-list-unstyled{padding-left:0; list-style:none;}
.w-embed:before,.w-embed:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-embed:after{clear:both;}
.w-video{width:100%; padding:0; position:relative;}
.w-video object{border:none; width:100%; height:100%; position:absolute; top:0; left:0;}
button,[type="button"],[type="reset"]{cursor:pointer; -webkit-appearance:button;}
.w-form{margin:0 0 15px;}
.w-form-done{text-align:center; background-color:#ddd; padding:20px; display:none;}
.w-form-fail{background-color:#ffdede; margin-top:10px; padding:10px; display:none;}
.w-input,.w-select{color:#333; vertical-align:middle; background-color:#fff; border:1px solid #ccc; width:100%; height:38px; margin-bottom:10px; padding:8px 12px; font-size:14px; line-height:1.42857; display:block;}
.w-input::placeholder,.w-select::placeholder{color:#999;}
.w-input:focus,.w-select:focus{border-color:#3898ec; outline:0;}
.w-input[disabled],.w-select[disabled]{cursor:not-allowed;}
.w-input[disabled]:not(.w-input-disabled),.w-select[disabled]:not(.w-input-disabled){background-color:#eee;}
textarea.w-input,textarea.w-select{height:auto;}
.w-select{background-color:#f3f3f3;}
.w-select[multiple]{height:auto;}
.w-form-label{cursor:pointer; margin-bottom:0; font-weight:normal; display:inline-block;}
.w-radio{margin-bottom:5px; padding-left:20px; display:block;}
.w-radio:before,.w-radio:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-radio:after{clear:both;}
.w-radio-input{float:left; margin:3px 0 0 -20px; line-height:normal;}
.w-file-upload{margin-bottom:10px; display:block;}
.w-file-upload-input{opacity:0; z-index:-100; width:.1px; height:.1px; position:absolute; overflow:hidden;}
.w-file-upload-default,.w-file-upload-uploading,.w-file-upload-success{color:#333; display:inline-block;}
.w-file-upload-error{margin-top:10px; display:block;}
.w-file-upload-default.w-hidden,.w-file-upload-uploading.w-hidden,.w-file-upload-error.w-hidden,.w-file-upload-success.w-hidden{display:none;}
.w-file-upload-uploading-btn{cursor:pointer; background-color:#fafafa; border:1px solid #ccc; margin:0; padding:8px 12px; font-size:14px; font-weight:normal; display:flex;}
.w-file-upload-file{background-color:#fafafa; border:1px solid #ccc; flex-grow:1; justify-content:space-between; margin:0; padding:8px 9px 8px 11px; display:flex;}
.w-file-upload-file-name{font-size:14px; font-weight:normal; display:block;}
.w-file-remove-link{cursor:pointer; width:auto; height:auto; margin-top:3px; margin-left:10px; padding:3px; display:block;}
.w-icon-file-upload-remove{margin:auto; font-size:10px;}
.w-file-upload-error-msg{color:#ea384c; padding:2px 0; display:inline-block;}
.w-file-upload-info{padding:0 12px; line-height:38px; display:inline-block;}
.w-file-upload-label{cursor:pointer; background-color:#fafafa; border:1px solid #ccc; margin:0; padding:8px 12px; font-size:14px; font-weight:normal; display:inline-block;}
.w-icon-file-upload-icon,.w-icon-file-upload-uploading{width:20px; margin-right:8px; display:inline-block;}
.w-icon-file-upload-uploading{height:20px;}
.w-container{max-width:940px; margin-left:auto; margin-right:auto;}
.w-container:before,.w-container:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-container:after{clear:both;}
.w-container .w-row{margin-left:-10px; margin-right:-10px;}
.w-row:before,.w-row:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-row:after{clear:both;}
.w-row .w-row{margin-left:0; margin-right:0;}
.w-col{float:left; width:100%; min-height:1px; padding-left:10px; padding-right:10px; position:relative;}
.w-col .w-col{padding-left:0; padding-right:0;}
.w-col-1{width:8.33333%;}
.w-col-2{width:16.6667%;}
.w-col-3{width:25%;}
.w-col-4{width:33.3333%;}
.w-col-5{width:41.6667%;}
.w-col-6{width:50%;}
.w-col-7{width:58.3333%;}
.w-col-8{width:66.6667%;}
.w-col-9{width:75%;}
.w-col-10{width:83.3333%;}
.w-col-11{width:91.6667%;}
.w-col-12{width:100%;}
.w-hidden-main{display:none !important;}
@media screen and (max-width:991px){
  .w-container{max-width:728px;}
  .w-hidden-main{display:inherit !important;}
  .w-hidden-medium{display:none !important;}
  .w-col-medium-1{width:8.33333%;}
  .w-col-medium-2{width:16.6667%;}
  .w-col-medium-3{width:25%;}
  .w-col-medium-4{width:33.3333%;}
  .w-col-medium-5{width:41.6667%;}
  .w-col-medium-6{width:50%;}
  .w-col-medium-7{width:58.3333%;}
  .w-col-medium-8{width:66.6667%;}
  .w-col-medium-9{width:75%;}
  .w-col-medium-10{width:83.3333%;}
  .w-col-medium-11{width:91.6667%;}
  .w-col-medium-12{width:100%;}
  .w-col-stack{width:100%; left:auto; right:auto;}
}
@media screen and (max-width:767px){
  .w-hidden-main,.w-hidden-medium{display:inherit !important;}
  .w-hidden-small{display:none !important;}
  .w-row,.w-container .w-row{margin-left:0; margin-right:0;}
  .w-col{width:100%; left:auto; right:auto;}
  .w-col-small-1{width:8.33333%;}
  .w-col-small-2{width:16.6667%;}
  .w-col-small-3{width:25%;}
  .w-col-small-4{width:33.3333%;}
  .w-col-small-5{width:41.6667%;}
  .w-col-small-6{width:50%;}
  .w-col-small-7{width:58.3333%;}
  .w-col-small-8{width:66.6667%;}
  .w-col-small-9{width:75%;}
  .w-col-small-10{width:83.3333%;}
  .w-col-small-11{width:91.6667%;}
  .w-col-small-12{width:100%;}
}
@media screen and (max-width:479px){
  .w-container{max-width:none;}
  .w-hidden-main,.w-hidden-medium,.w-hidden-small{display:inherit !important;}
  .w-hidden-tiny{display:none !important;}
  .w-col{width:100%;}
  .w-col-tiny-1{width:8.33333%;}
  .w-col-tiny-2{width:16.6667%;}
  .w-col-tiny-3{width:25%;}
  .w-col-tiny-4{width:33.3333%;}
  .w-col-tiny-5{width:41.6667%;}
  .w-col-tiny-6{width:50%;}
  .w-col-tiny-7{width:58.3333%;}
  .w-col-tiny-8{width:66.6667%;}
  .w-col-tiny-9{width:75%;}
  .w-col-tiny-10{width:83.3333%;}
  .w-col-tiny-11{width:91.6667%;}
  .w-col-tiny-12{width:100%;}
}
.w-widget{position:relative;}
.w-widget-map{width:100%; height:400px;}
.w-widget-map label{width:auto; display:inline;}
.w-widget-map img{max-width:inherit;}
.w-widget-twitter{overflow:hidden;}
.w-widget-twitter-count-shim{vertical-align:top; text-align:center; background:#fff; border:1px solid #758696; border-radius:3px; width:28px; height:20px; display:inline-block; position:relative;}
.w-widget-twitter-count-shim *{pointer-events:none; -webkit-user-select:none; user-select:none;}
.w-widget-twitter-count-shim .w-widget-twitter-count-inner{text-align:center; color:#999; font-family:serif; font-size:15px; line-height:12px; position:relative;}
.w-widget-twitter-count-shim .w-widget-twitter-count-clear{display:block; position:relative;}
.w-widget-twitter-count-shim.w--large{width:36px; height:28px;}
.w-widget-twitter-count-shim.w--large .w-widget-twitter-count-inner{font-size:18px; line-height:18px;}
.w-widget-twitter-count-shim:not(.w--vertical){margin-left:5px; margin-right:8px;}
.w-widget-twitter-count-shim:not(.w--vertical).w--large{margin-left:6px;}
.w-widget-twitter-count-shim:not(.w--vertical):before,.w-widget-twitter-count-shim:not(.w--vertical):after{content:" "; pointer-events:none; border:solid #0000; width:0; height:0; position:absolute; top:50%; left:0;}
.w-widget-twitter-count-shim:not(.w--vertical):before{border-width:4px; border-color:#75869600 #5d6c7b #75869600 #75869600; margin-top:-4px; margin-left:-9px;}
.w-widget-twitter-count-shim:not(.w--vertical).w--large:before{border-width:5px; margin-top:-5px; margin-left:-10px;}
.w-widget-twitter-count-shim:not(.w--vertical):after{border-width:4px; border-color:#fff0 #fff #fff0 #fff0; margin-top:-4px; margin-left:-8px;}
.w-widget-twitter-count-shim:not(.w--vertical).w--large:after{border-width:5px; margin-top:-5px; margin-left:-9px;}
.w-widget-twitter-count-shim.w--vertical{width:61px; height:33px; margin-bottom:8px;}
.w-widget-twitter-count-shim.w--vertical:before,.w-widget-twitter-count-shim.w--vertical:after{content:" "; pointer-events:none; border:solid #0000; width:0; height:0; position:absolute; top:100%; left:50%;}
.w-widget-twitter-count-shim.w--vertical:before{border-width:5px; border-color:#5d6c7b #75869600 #75869600; margin-left:-5px;}
.w-widget-twitter-count-shim.w--vertical:after{border-width:4px; border-color:#fff #fff0 #fff0; margin-left:-4px;}
.w-widget-twitter-count-shim.w--vertical .w-widget-twitter-count-inner{font-size:18px; line-height:22px;}
.w-widget-twitter-count-shim.w--vertical.w--large{width:76px;}
.w-background-video{color:#fff; height:500px; position:relative; overflow:hidden;}
.w-background-video--control{background-color:#0000; padding:0; position:absolute; bottom:1em; right:1em;}
.w-background-video--control > [hidden]{display:none !important;}
.w-slider{text-align:center; clear:both; -webkit-tap-highlight-color:#0000; tap-highlight-color:#0000; background:#ddd; height:300px; position:relative;}
.w-slider-mask{z-index:1; white-space:nowrap; height:100%; display:block; position:relative; left:0; right:0; overflow:hidden;}
.w-slide{vertical-align:top; white-space:normal; text-align:left; width:100%; height:100%; display:inline-block; position:relative;}
.w-slider-nav{z-index:2; text-align:center; -webkit-tap-highlight-color:#0000; tap-highlight-color:#0000; height:40px; margin:auto; padding-top:10px; position:absolute; inset:auto 0 0;}
.w-slider-nav.w-round > div{border-radius:100%;}
.w-slider-nav.w-num > div{font-size:inherit; line-height:inherit; width:auto; height:auto; padding:.2em .5em;}
.w-slider-nav.w-shadow > div{box-shadow:0 0 3px #3336;}
.w-slider-nav-invert{color:#fff;}
.w-slider-nav-invert > div{background-color:#2226;}
.w-slider-nav-invert > div.w-active{background-color:#222;}
.w-slider-dot{cursor:pointer; background-color:#fff6; width:1em; height:1em; margin:0 3px .5em; transition:background-color .1s,color .1s; display:inline-block; position:relative;}
.w-slider-dot.w-active{background-color:#fff;}
.w-slider-dot:focus{outline:none; box-shadow:0 0 0 2px #fff;}
.w-slider-dot:focus.w-active{box-shadow:none;}
.w-slider-arrow-left,.w-slider-arrow-right{cursor:pointer; color:#fff; -webkit-tap-highlight-color:#0000; tap-highlight-color:#0000; -webkit-user-select:none; user-select:none; width:80px; margin:auto; font-size:40px; position:absolute; inset:0; overflow:hidden;}
.w-slider-arrow-left:focus,.w-slider-arrow-right:focus{outline:0;}
.w-slider-arrow-left{z-index:3; right:auto;}
.w-slider-arrow-right{z-index:4; left:auto;}
.w-icon-slider-left,.w-icon-slider-right{width:1em; height:1em; margin:auto; inset:0;}
.w-slider-aria-label{clip:rect(0 0 0 0); border:0; width:1px; height:1px; margin:-1px; padding:0; position:absolute; overflow:hidden;}
.w-slider-force-show{display:block !important;}
.w-dropdown{text-align:left; z-index:900; margin-left:auto; margin-right:auto; display:inline-block; position:relative;}
.w-dropdown-btn,.w-dropdown-toggle,.w-dropdown-link{vertical-align:top; color:#222; text-align:left; white-space:nowrap; margin-left:auto; margin-right:auto; padding:20px; text-decoration:none; position:relative;}
.w-dropdown-toggle{-webkit-user-select:none; user-select:none; cursor:pointer; padding-right:40px; display:inline-block;}
.w-dropdown-toggle:focus{outline:0;}
.w-icon-dropdown-toggle{width:1em; height:1em; margin:auto 20px auto auto; position:absolute; top:0; bottom:0; right:0;}
.w-dropdown-list{background:#ddd; min-width:100%; display:none; position:absolute;}
.w-dropdown-list.w--open{display:block;}
.w-dropdown-link{color:#222; padding:10px 20px; display:block;}
.w-dropdown-link.w--current{color:#0082f3;}
.w-dropdown-link:focus{outline:0;}
@media screen and (max-width:767px){
  .w-nav-brand{padding-left:10px;}
}
.w-lightbox-backdrop{cursor:auto; letter-spacing:normal; text-indent:0; text-shadow:none; text-transform:none; visibility:visible; white-space:normal; word-break:normal; word-spacing:normal; word-wrap:normal; color:#fff; text-align:center; z-index:2000; opacity:0; -webkit-user-select:none; -moz-user-select:none; -webkit-tap-highlight-color:transparent; background:#000000e6; outline:0; font-family:Helvetica Neue,Helvetica,Ubuntu,Segoe UI,Verdana,sans-serif; font-size:17px; font-style:normal; font-weight:300; line-height:1.2; list-style:disc; position:fixed; inset:0; -webkit-transform:translate(0);}
.w-lightbox-backdrop,.w-lightbox-container{-webkit-overflow-scrolling:touch; height:100%; overflow:auto;}
.w-lightbox-content{height:100vh; position:relative; overflow:hidden;}
.w-lightbox-view{opacity:0; width:100vw; height:100vh; position:absolute;}
.w-lightbox-view:before{content:""; height:100vh;}
.w-lightbox-group,.w-lightbox-group .w-lightbox-view,.w-lightbox-group .w-lightbox-view:before{height:86vh;}
.w-lightbox-frame,.w-lightbox-view:before{vertical-align:middle; display:inline-block;}
.w-lightbox-figure{margin:0; position:relative;}
.w-lightbox-group .w-lightbox-figure{cursor:pointer;}
.w-lightbox-img{width:auto; max-width:none; height:auto;}
/* .w-lightbox-image{float:none; max-width:100vw; max-height:100vh; display:block;} */
/* .w-lightbox-group .w-lightbox-image{max-height:86vh;} */
.w-lightbox-caption{text-align:left; text-overflow:ellipsis; white-space:nowrap; background:#0006; padding:.5em 1em; position:absolute; bottom:0; left:0; right:0; overflow:hidden;}
.w-lightbox-embed{width:100%; height:100%; position:absolute; inset:0;}
.w-lightbox-control{cursor:pointer; background-position:center; background-repeat:no-repeat; background-size:24px; width:4em; transition:all .3s; position:absolute; top:0;}
.w-lightbox-left{background-image:url("data:image/svg+xml; base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii0yMCAwIDI0IDQwIiB3aWR0aD0iMjQiIGhlaWdodD0iNDAiPjxnIHRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0ibTAgMGg1djIzaDIzdjVoLTI4eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDN2MjNoMjN2M2gtMjZ6IiBmaWxsPSIjZmZmIi8+PC9nPjwvc3ZnPg=="); display:none; bottom:0; left:0;}
.w-lightbox-right{background-image:url("data:image/svg+xml; base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMjQgNDAiIHdpZHRoPSIyNCIgaGVpZ2h0PSI0MCI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMC0waDI4djI4aC01di0yM2gtMjN6IiBvcGFjaXR5PSIuNCIvPjxwYXRoIGQ9Im0xIDFoMjZ2MjZoLTN2LTIzaC0yM3oiIGZpbGw9IiNmZmYiLz48L2c+PC9zdmc+"); display:none; bottom:0; right:0;}
.w-lightbox-close{background-image:url("data:image/svg+xml; base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMTggMTciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxNyI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMCAwaDd2LTdoNXY3aDd2NWgtN3Y3aC01di03aC03eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDd2LTdoM3Y3aDd2M2gtN3Y3aC0zdi03aC03eiIgZmlsbD0iI2ZmZiIvPjwvZz48L3N2Zz4="); background-size:18px; height:2.6em; right:0;}
.w-lightbox-strip{white-space:nowrap; padding:0 1vh; line-height:0; position:absolute; bottom:0; left:0; right:0; overflow:auto hidden;}
.w-lightbox-item{box-sizing:content-box; cursor:pointer; width:10vh; padding:2vh 1vh; display:inline-block; -webkit-transform:translate3d(0,0,0);}
.w-lightbox-active{opacity:.3;}
.w-lightbox-thumbnail{background:#222; height:10vh; position:relative; overflow:hidden;}
.w-lightbox-thumbnail-image{position:absolute; top:0; left:0;}
.w-lightbox-thumbnail .w-lightbox-tall{width:100%; top:50%; transform:translate(0,-50%);}
.w-lightbox-thumbnail .w-lightbox-wide{height:100%; left:50%; transform:translate(-50%);}
.w-lightbox-spinner{box-sizing:border-box; border:5px solid #0006; border-radius:50%; width:40px; height:40px; margin-top:-20px; margin-left:-20px; animation:.8s linear infinite spin; position:absolute; top:50%; left:50%;}
.w-lightbox-spinner:after{content:""; border:3px solid #0000; border-bottom-color:#fff; border-radius:50%; position:absolute; inset:-4px;}
.w-lightbox-hide{display:none;}
.w-lightbox-noscroll{overflow:hidden;}
@media (min-width:768px){
  .w-lightbox-content{height:96vh; margin-top:2vh;}
  .w-lightbox-view,.w-lightbox-view:before{height:96vh;}
  .w-lightbox-group,.w-lightbox-group .w-lightbox-view,.w-lightbox-group .w-lightbox-view:before{height:84vh;}
  /* .w-lightbox-image{max-width:96vw; max-height:96vh;} */
  /* .w-lightbox-group .w-lightbox-image{max-width:82.3vw; max-height:84vh;} */
  .w-lightbox-left,.w-lightbox-right{opacity:.5; display:block;}
  .w-lightbox-close{opacity:.8;}
  .w-lightbox-control:hover{opacity:1;}
}
.w-lightbox-inactive,.w-lightbox-inactive:hover{opacity:0;}
.w-richtext:before,.w-richtext:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-richtext:after{clear:both;}
.w-richtext ol,.w-richtext ul{overflow:hidden;}
.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-video div:after,.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-image div{outline:2px solid #2895f7;}
.w-richtext figure.w-richtext-figure-type-video > div:after{content:""; display:none; position:absolute; inset:0;}
.w-richtext figure{max-width:60%; position:relative;}
.w-richtext figure > div:before{cursor:default !important;}
.w-richtext figure img{width:100%;}
.w-richtext figure div{color:#0000; font-size:0;}
.w-richtext figure.w-richtext-figure-type-image{display:table;}
.w-richtext figure.w-richtext-figure-type-image > div{display:inline-block;}
.w-richtext figure.w-richtext-figure-type-video{width:60%; height:0;}
.w-richtext figure.w-richtext-figure-type-video > div{width:100%;}
.w-richtext figure.w-richtext-align-center{clear:both; margin-left:auto; margin-right:auto;}
.w-richtext figure.w-richtext-align-center.w-richtext-figure-type-image > div{max-width:100%;}
.w-richtext figure.w-richtext-align-normal{clear:both;}
.w-richtext figure.w-richtext-align-fullwidth{text-align:center; clear:both; width:100%; max-width:100%; margin-left:auto; margin-right:auto; display:block;}
.w-richtext figure.w-richtext-align-fullwidth > div{padding-bottom:inherit; display:inline-block;}
.w-richtext figure.w-richtext-align-floatleft{float:left; clear:none; margin-right:15px;}
.w-richtext figure.w-richtext-align-floatright{float:right; clear:none; margin-left:15px;}
.w-nav{z-index:10; background:#ddd; position:relative;}
.w-nav:before,.w-nav:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-nav:after{clear:both;}
.w-nav-brand{ color:#333; text-decoration:none; position:relative;display: inline-block;}
.w-nav-link{vertical-align:top; color:#222; text-align:left; margin-left:auto; margin-right:auto; padding:20px; text-decoration:none; display:inline-block; position:relative;}
.w-nav-link.w--current{color:#0082f3;}
.w-nav-menu{float:right; position:relative;}
[data-nav-menu-open]{text-align:center; background:#c8c8c8; min-width:200px; position:absolute; top:100%; left:0; right:0; overflow:visible; display:block !important;}
.w--nav-link-open{display:block; position:relative;}
.w-nav-overlay{width:100%; display:none; position:absolute; top:100%; left:0; right:0; overflow:hidden;}
.w-nav-overlay [data-nav-menu-open]{top:0;}
.w-nav-button{float:right; cursor:pointer; -webkit-tap-highlight-color:#0000; tap-highlight-color:#0000; -webkit-user-select:none; user-select:none; padding:18px; font-size:24px; display:none; position:relative;}
.w-nav-button:focus{outline:0;}
.w-nav-button.w--open{color:#fff; background-color:#c8c8c8;}
.w--nav-dropdown-open,.w--nav-dropdown-toggle-open{display:block;}
.w--nav-dropdown-list-open{position:static;}
@media screen and (max-width:767px){
  .w-nav-brand{padding-left:10px;}
}
.w-tabs{position:relative;}
.w-tabs:before,.w-tabs:after{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-tabs:after{clear:both;}
.w-tab-menu{position:relative;}
.w-tab-link{vertical-align:top; text-align:left; cursor:pointer; color:#222; background-color:#ddd; padding:9px 30px; text-decoration:none; display:inline-block; position:relative;}
.w-tab-link.w--current{background-color:#c8c8c8;}
.w-tab-link:focus{outline:0;}
.w-tab-content{display:block; position:relative; overflow:hidden;}
.w-tab-pane{display:none; position:relative;}
.w--tab-active{display:block;}
@media screen and (max-width:479px){
  .w-tab-link{display:block;}
}
.w-ix-emptyfix:after{content:"";}
@keyframes spin{
  0%{transform:rotate(0);}
  100%{transform:rotate(360deg);}
}
.w-dyn-empty{background-color:#ddd; padding:10px;}
.w-dyn-hide,.w-dyn-bind-empty,.w-condition-invisible{display:none !important;}
.w-code-component > *{width:100%; height:100%; position:absolute; top:0; left:0;}

/* Screen reader only class - visually hidden but accessible to screen readers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
:root{--color--white:white; --font-family--body:Poppins,sans-serif; --color--body:#2B2B2B; --heading-family:Helveticanowdisplay,Arial,sans-serif; --color--black:var(--color--black); --color--prime:#DD6826; --border-radius--md:20px; --color--accent:#F3F3EF; --border-radius:16px; --color--secondary-2:#ced7e9; --color--gary-3:#b3b5bc; --color--secondary-1:#d2dcfc; --color--black:#1C4928; --border-radius--lg:50px; --color--gray-1:#20242f; --heading-family-alt:"Instrument Serif",sans-serif; --color--primary-2:#ff7c30; --border-radius--sm:10px; --color--secondary-3:#bddbf5; --color--secondary-4:#e3f5fd; --color--transparent:transparent; --border-radius--xs:5px;}
.w-layout-blockcontainer{max-width:940px; margin-left:auto; margin-right:auto; display:block;}
.w-layout-grid{grid-row-gap:16px; grid-column-gap:16px; grid-template-rows:auto auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.w-form-formradioinput--inputType-custom{border:1px solid #ccc; border-radius:50%; width:12px; height:12px;}
.w-form-formradioinput--inputType-custom.w--redirected-focus{box-shadow:0 0 3px 1px #3898ec;}
.w-form-formradioinput--inputType-custom.w--redirected-checked{border-width:4px; border-color:#3898ec;}
.w-checkbox{margin-bottom:5px; padding-left:20px; display:block;}
.w-checkbox:before{content:" "; grid-area:1 / 1 / 2 / 2; display:table;}
.w-checkbox:after{content:" "; clear:both; grid-area:1 / 1 / 2 / 2; display:table;}
.w-checkbox-input{float:left; margin:4px 0 0 -20px; line-height:normal;}
.w-checkbox-input--inputType-custom{border:1px solid #ccc; border-radius:2px; width:12px; height:12px;}
.w-checkbox-input--inputType-custom.w--redirected-checked{background-color:#3898ec; background-image:url("https://d3e54v103j8qbb.cloudfront.net/static/custom-checkbox-checkmark.589d534424.svg"); background-position:50%; background-repeat:no-repeat; background-size:cover; border-color:#3898ec;}
.w-checkbox-input--inputType-custom.w--redirected-focus{box-shadow:0 0 3px 1px #3898ec;}
.w-pagination-wrapper{flex-wrap:wrap; justify-content:center; display:flex;}
.w-pagination-previous{color:#333; background-color:#fafafa; border:1px solid #ccc; border-radius:2px; margin-left:10px; margin-right:10px; padding:9px 20px; font-size:14px; display:block;}
.w-pagination-previous-icon{margin-right:4px;}
.w-page-count{text-align:center; width:100%; margin-top:20px;}
.w-pagination-next{color:#333; background-color:#fafafa; border:1px solid #ccc; border-radius:2px; margin-left:10px; margin-right:10px; padding:9px 20px; font-size:14px; display:block;}
.w-pagination-next-icon{margin-left:4px;}
@media screen and (max-width:991px){
  .w-layout-blockcontainer{max-width:728px;}
}
@media screen and (max-width:767px){
  .w-layout-blockcontainer{max-width:none;}
}
body{background-color:var(--color--white); font-family:var(--font-family--body); color:var(--color--body); font-size:18px; font-weight:400; line-height:1.6em;}
h1{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:60px; font-weight:500; line-height:1.3em;}
h2{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:42px; font-weight:500; line-height:1.4em;}
h3{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:36px; font-weight:500; line-height:1.4em;}
h4{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:30px; font-weight:500; line-height:1.4em;}
h5{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:26px; font-weight:500; line-height:1.4em;}
h6{font-family:var(--heading-family); color:var(--color--black); margin-top:0; margin-bottom:10px; font-size:22px; font-weight:500; line-height:1.4em;}
p{margin-bottom:10px;}
a{color:var(--color--body); text-decoration:underline;}
ul,ol{margin-top:0; margin-bottom:10px; padding-left:0;}
li{margin-bottom:10px;}
img{max-width:100%; display:inline-block;}
label{margin-bottom:10px; font-weight:400; display:block;line-height: 1.4;}
strong{font-weight:bold;}
em{font-style:italic;}


/* My Styles */

blockquote {
    border: 1px solid var(--color--prime);
    border-radius: 10px;
    background-color: var(--color--accent);
    color: var(--color--black);
    text-align: center;
    margin: 0;
    padding: 16px;
    font-size: 17px;
    line-height: 28px;
}

.richText blockquote p{margin: 0;}
figure{margin-bottom:10px;}
.bg-prime{background-color:var(--color--prime);}
.text-prime{color:var(--color--prime);}
.text-dark{color:var(--color--black);}
.bg-accent{background-color:var(--color--accent);}
.bg-sec{background-color:var(--color--black);}

.pb-0{padding-bottom:0 !important;}
.text-right{text-align:right;}
.text-underline{text-decoration:underline;}
.container-medium{max-width:1220px; margin-left:auto; margin-right:auto; padding-left:30px; padding-right:30px;}
.text-small{font-size:16px; line-height:1.4em;}
.text-center{text-align:center;}
.form-input {
    border: 1px solid var(--color--secondary-2);
    border-radius: var(--border-radius--sm);
    background-color: var(--color--accent);
    color: var(--color--black);
    height: 48px;
    margin-bottom: 15px;
    padding: 10px 14px;
    font-size: 18px;
    font-weight: 400;
    line-height: 1.4em;
    transition: all .3s ease-in-out;
}
.form-input:focus{border-color:var(--color--prime); outline: none; box-shadow: none;}
.form-input::placeholder{color:#777; font-size:16px; font-weight:400 !important; line-height:1.4em;}
.form-input.form-textarea{height:150px;}
.form-input.form-input-footer{border-color:var(--color--gray-1); background-color:var(--color--gray-1); color:var(--color--white); margin-bottom:0; padding-right:60px;}
.form-input.form-input-footer:focus{border-color:var(--color--prime);}
.form-input.form-input-footer::placeholder{color:var(--color--gary-3);}
.form-input.form-input-dark{border-color:var(--color--black); background-color:var(--color--gray-1); color:var(--color--white);}
.form-input.form-input-dark:focus{border-color:var(--color--prime);}
.form-input.form-input-dark::placeholder{color:var(--color--white);}
.no-margin{margin-bottom:0;}
.form-select{border:1px solid var(--color--secondary-2); border-radius:var(--border-radius--lg); background-color:var(--color--accent); color:var(--color--body); height:54px; margin-bottom:15px; padding:10px 20px; font-size:18px; font-weight:400; line-height:1.4em; transition:all .3s ease-in-out;}
.form-select:focus{border-color:var(--color--black); color:var(--color--black);}
.form-select::placeholder{color:var(--color--body) ; font-size:16px;}
.text-delete{text-decoration:line-through;}
.container-box{border-radius:var(--border-radius--md); background-color:var(--color--accent); text-align:center; padding:15px 20px;}
.text-lead{font-size:20px; line-height:1.5em;}
.text-left{text-align:left;}
.button-black{grid-column-gap:10px; grid-row-gap:10px; border-radius:var(--border-radius--lg); background-color:var(--color--black); color:var(--color--white); text-align:center; border-style:solid; border-width:0; padding:14px 40px; font-size:18px; font-weight:400; line-height:1.4em; text-decoration:none; transition:all .3s ease-in-out;}
.button-black:hover{background-color:var(--color--prime); color:var(--color--white); transform:scale(.95);}
.button-black.nav-button{padding-left:30px; padding-right:30px;}
.heading-serif{font-family:var(--heading-family-alt); font-style:italic; font-weight:400;}
.checkbox{border:1px solid var(--color--gary-3); border-radius:3px; width:auto; min-width:20px; height:auto; min-height:20px; margin-top:3px;}
.checkbox.w--redirected-checked{border-color:var(--color--gary-3); background-color:var(--color--prime); background-image:url("https://cdn.prod.website-files.com/6777c6ca4cd4fd1a5c59b396/6778b500ddcd251706da36db_checkmark.svg"); background-position:50%; background-size:12px 12px; border-radius:3px;}
.checkbox.w--redirected-focus{border-color:var(--color--gary-3); box-shadow:none; border-radius:3px; transition:background-color .3s ease-in-out;}
.text-mark{background-color:var(--color--prime); color:var(--color--white); padding-left:3px; padding-right:3px;}
.text-bold{font-weight:600;}
.bg-prime{background-color:var(--color--prime);}
.bg-primary-2{background-color:var(--color--primary-2);}
.bg-black{background-color:var(--color--black);}
.bg-white{background-color:var(--color--white);}
.container{max-width:1460px; margin-left:auto; margin-right:auto; padding-left:30px; padding-right:30px;}
.container-small{max-width:1020px; margin-left:auto; margin-right:auto; padding-left:30px; padding-right:30px;}
.inner-container{max-width:820px; margin-left:auto; margin-right:auto; padding-left:30px; padding-right:30px;}
.inner-container.left{margin-left:0;}
.section-spacing{padding-top:100px; padding-bottom:100px;}
.section-spacing-top{padding-top:100px;}
.section-spacing-bottom{padding-bottom:100px;}
.hero-inner{padding-top:30px;}
.hero-inner-content{border-radius:var(--border-radius--md); background-color:var(--color--black); color:var(--color--gary-3); text-align:center; padding:50px;}
.hero-inner-title{color:var(--color--white); font-size:60px;}
.brand-wrap{padding-left:0;}
.nav-right{z-index:1; grid-column-gap:20px; grid-row-gap:20px; justify-content:center; align-items:center; display:flex;}
.navbar{background-color:#0000; padding-top:30px; padding-bottom:30px;}
.grid-footer-link-wrap{grid-column-gap:10px; grid-row-gap:10px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.grid-footer-link-inner{grid-column-gap:15px; grid-row-gap:15px; flex-direction:column; align-items:flex-start; display:flex;}
.bg-gray-1{background-color:var(--color--gray-1);}
.bg-gray-2{background-color:var(--color--body);}
.brand{width:100%; height:80px;}
.grid-navbar{grid-column-gap:20px; grid-row-gap:20px; grid-template-rows:auto; grid-template-columns:1fr auto 1fr; align-items:center;}
.grid-footer{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:.7fr 1fr;}
.contact-wrap{grid-column-gap:20px; grid-row-gap:10px; flex-flow:wrap; justify-content:flex-start; align-items:center; display:flex;}
.nav-link{color:var(--color--black); text-align:justify; margin:5px 20px; padding:0; font-size:18px; font-weight:500; line-height:1.2em; transition:color .3s ease-in-out;}
.nav-link:hover,.nav-link.w--current{color:var(--color--prime);}
.bg-gray-3{background-color:var(--color--gary-3);}
.bg-accent{background-color:var(--color--accent);}
.bg-secondary-1{background-color:var(--color--secondary-1);}
.bg-secondary-2{background-color:var(--color--secondary-2);}
.bg-secondary-3{background-color:var(--color--secondary-3);}
.bg-secondary-4{background-color:var(--color--secondary-4);}
.gradient-1{background-image:linear-gradient(180deg,var(--color--primary-2),var(--color--prime));}
.gradient-2{background-image:linear-gradient(180deg,var(--color--secondary-1),var(--color--secondary-3) 49%,var(--color--secondary-4));}
.button-gradient{border-radius:var(--border-radius--lg); background-color:var(--color--prime); background-image:linear-gradient(180deg,var(--color--primary-2),var(--color--transparent)); color:var(--color--white); text-align:center; border-style:solid; border-width:0; padding:14px 40px; font-size:18px; font-weight:400; line-height:1.4em; transition:all .3s ease-in-out;}
.button-gradient:hover{background-color:var(--color--primary-2); color:var(--color--white); transform:scale(.95);}
.button-gradient.button-full{flex:none;}
.grid-footer-link{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.grid-footer-contact{grid-column-gap:50px; grid-row-gap:50px; border-bottom:1px solid var(--color--gray-1); grid-template-rows:auto; grid-template-columns:.7fr 1fr; margin-top:50px; padding-bottom:50px;}
.contact-link{opacity:.8; color:var(--color--gary-3); font-size:18px; font-weight:400; line-height:1.4em; text-decoration:none; transition:all .3s ease-in-out;}
.contact-link:hover{opacity:1; color:var(--color--white);}
.text-white{color:var(--color--white);}
.hero-section{margin-top:-140px; padding-top:200px; position:relative; overflow:hidden;}
.hero-content-center{z-index:2; text-align:center; width:90%; margin-left:auto; margin-right:auto; position:relative;}
.hero-title-center{margin-bottom:30px; font-size:74px;}
.hero-button-list{grid-column-gap:30px; grid-row-gap:15px; flex-flow:wrap; justify-content:center; align-items:center; margin-top:40px; display:flex;}
.button-icon{flex:none; width:24px; height:24px;}
.button-secondary-2-outline{grid-column-gap:10px; grid-row-gap:10px; border:1px solid var(--color--secondary-2); border-radius:var(--border-radius--lg); background-color:var(--color--white); color:var(--color--black); justify-content:center; align-items:center; padding:14px 40px; font-size:18px; font-weight:500; text-decoration:none; transition:all .3s ease-in-out; display:flex;}
.button-secondary-2-outline:hover{background-color:var(--color--accent); transform:scale(.95);}
.hero-image-wrap{z-index:2; border-radius:var(--border-radius); justify-content:center; align-items:center; max-height:768px; margin-top:120px; margin-left:30px; margin-right:30px; display:flex; position:relative; overflow:hidden;}
.hero-image{object-fit:cover; width:100%; height:100%; min-height:750px;}
.hero-blur{filter:blur(50px); width:120%; height:700px; margin-left:-10%; position:absolute; inset:-100px 0% auto;}
.hero-blur-divider{z-index:1; background-color:var(--color--white); filter:blur(50px); border-radius:100%; width:120%; height:550px; margin-left:-10%; position:absolute; inset:470px 0% auto;}
.about-section{padding-top:100px; position:relative; overflow:hidden;}
.about-content-right{z-index:2; width:75%; margin-left:auto; position:relative;}
.about-title-right{margin-bottom:30px; font-size:26px;}
.grid-about-counter{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; margin-top:50px; display:grid;}
.about-counter-title{margin-bottom:15px; font-size:48px; line-height:1em;}
.text-primary-1{color:var(--color--prime);}
.vector-01{width:450px; height:450px; margin-top:auto; margin-bottom:auto; position:absolute; inset:0% auto 0% -220px;}
.vector-01.sm{left:-320px;}
.immigration-collection-list-wrapper{height:100%;}
.section-title{margin-bottom:50px;}
.section-title.immigration-section-title{width:85%;}
.section-title.section-title-inline{grid-column-gap:15px; grid-row-gap:15px; flex-flow:wrap; justify-content:space-between; align-items:center; display:flex;}
.immigration-collection-list{height:100%;}
.immigration-wrap{position:relative;}
.immigration-collection-item{height:100%;}
.immigration-section{position:relative; overflow:hidden;}
.text-italic{font-style:italic;}
.immigration-item{border-radius:var(--border-radius); background-color:var(--color--accent); flex-flow:column; height:100%; text-decoration:none; display:flex; overflow:hidden;}
.immigration-image-wrap{position:relative; overflow:hidden;}
.immigration-image{object-fit:cover; width:100%; height:100%;}
.immigration-content{flex:1; padding:20px;}
.immigration-title{margin-bottom:15px; font-size:24px;}
.immigration-arrow-wrap{z-index:2; background-color:var(--color--black); border-radius:50%; justify-content:center; align-items:center; width:60px; height:60px; display:flex; position:absolute; inset:30px 30px auto auto;}
.immigration-arrow{width:16px; height:16px;}
.step-section{position:relative; overflow:hidden;}
.grid-step{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.step-item{z-index:2; text-align:center; flex-flow:column; justify-content:flex-start; align-items:center; display:flex; position:relative;}
.step-item.center{margin-top:30px;}
.step-item.first{margin-top:0;}
.step-number{font-family:var(--heading-family); color:var(--color--white); border-radius:50%; justify-content:center; align-items:center; width:70px; height:70px; margin-bottom:20px; font-size:28px; font-weight:500; line-height:1.4em; display:flex;}
.step-title{margin-bottom:15px; font-size:28px; line-height:1.1em;}
.vector-02{width:100%; height:auto; position:absolute; top:-10px; right:30px;}
.step-wrap{position:relative;}
.countries-section-title{color:var(--color--white); margin-bottom:30px;}
.grid-visa-type{z-index:2; grid-column-gap:0; grid-row-gap:0; grid-template-rows:auto; grid-template-columns:1fr .85fr; grid-auto-columns:1fr; display:grid; position:relative;}
.grid-visa-inline{grid-column-gap:10px; grid-row-gap:20px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; align-items:start; margin-top:30px; display:grid;}
.accordion-wrap{border-bottom:1px solid var(--color--secondary-2); padding-top:30px; padding-bottom:30px;}
.accordion-line-vr{background-color:var(--color--prime); width:2px; height:18px; margin:auto; position:absolute;}
.accordion-line-vr.open{transform:rotate(90deg);}
.accordion-title{color:var(--color--black); align-self:center; margin-bottom:0; font-size:24px; font-weight:500; line-height:1.3em;}
.accordion-title.open{color:var(--color--prime);}
.accordion-line-wrap{flex:none; justify-content:center; align-items:center; width:18px; height:18px; margin-top:5px; display:flex; position:relative;}
.accordion-content-wrap{overflow:hidden;}
.accordion-heading{grid-column-gap:12px; grid-row-gap:12px; cursor:pointer; justify-content:space-between; align-items:flex-start; display:flex;}
.accordion-list{z-index:2; position:relative;}
.accordion-line-hr{background-color:var(--color--prime); width:18px; height:2px;}
.accordion-content{padding-top:15px;}
.button-link{text-underline-position:under; font-size:18px; font-weight:500; line-height:1.4em; text-decoration:underline; transition:text-decoration-color .3s ease-in-out;}
.button-link:hover{-webkit-text-decoration-color:var(--color--transparent); text-decoration-color:var(--color--transparent);}
.hero-image-right-wrap{border-top-left-radius:var(--border-radius); border-bottom-left-radius:var(--border-radius); width:52%; position:absolute; inset:0 -1% 0 auto; overflow:hidden;}
.hero-image-right{object-fit:cover; width:100%; height:100%;}
.hero-content-left{z-index:2; width:45%; padding-top:30px; padding-bottom:30px; position:relative;}
.hero-title-left{margin-bottom:15px; font-size:60px;}
.hero-section-split{position:relative; overflow:hidden;}
.grid-hero-info{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-top:50px; display:grid;}
.hero-info-icon-wrap{background-color:var(--color--black); border-radius:50%; justify-content:center; align-items:center; width:60px; height:60px; margin-bottom:15px; display:flex;}
.hero-info-icon{width:20px; height:20px;}
.hero-info-text{color:var(--color--black); font-size:16px; font-weight:500; line-height:1.4em;}
.hero-info-text.first{margin-bottom:5px;}
.grid-immigration{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.button-center{text-align:center; margin-top:50px;}
.grid-why-choose{grid-column-gap:100px; grid-row-gap:100px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-bottom:80px; display:grid; position:relative;}
.text-gray-3{color:var(--color--gary-3);}
.grid-why-choose-counter{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.grid-visa-split{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:.5fr 1fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.cta-wrap{border-radius:var(--border-radius); position:relative; overflow:hidden;}
.cta-title{color:var(--color--white); margin-bottom:0;}
.vector-06{width:450px; height:450px; margin-top:auto; margin-bottom:auto; position:absolute; inset:0 -50px 0% auto;}
.cta-feature-list{grid-column-gap:20px; grid-row-gap:10px; flex-flow:wrap; margin-top:30px; margin-bottom:40px; display:flex;}
.cta-feature-list-item{grid-column-gap:6px; grid-row-gap:6px; color:var(--color--white); justify-content:flex-start; align-items:flex-start; display:flex;}
.cta-feature-list-icon{flex:none; margin-top:0;}
.cta-content-wrap{width:60%; padding-top:30px; padding-bottom:30px; padding-left:30px;}
.agent-image{object-fit:cover; width:100%; height:100%;}
.grid-contact-split{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.grid-contact-form-inner{grid-column-gap:30px; grid-row-gap:10px; grid-template-rows:auto auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-bottom:0; display:grid;}
.contact-split-image-wrap{border-radius:var(--border-radius); overflow:hidden;}
.contact-split-image{object-fit:cover; width:100%; height:100%;}
.sidebar-sticky{position:sticky; top:50px;}
.sidebar-item{border-radius:var(--border-radius); background-color:var(--color--black); color:var(--color--gary-3); padding:30px;}
.sidebar-contact{grid-column-gap:20px; grid-row-gap:20px; flex-direction:column; display:flex;}
/* .sidebar-item-inner{border-bottom:1px solid var(--color--gray-1); margin-bottom:40px; padding-bottom:50px;} */
.contact-link-wrap{grid-column-gap:12px; grid-row-gap:12px; flex-flow:wrap; justify-content:flex-start; align-items:flex-start; display:flex;}
.contact-link-icon{flex:none; width:24px; height:24px;}
.section-spacing-sm{padding-top:80px; padding-bottom:80px;}
.about-hero-section{padding-top:50px;}
.about-hero-title{margin-bottom:0;}
.grid-about-counter-center{grid-column-gap:30px; grid-row-gap:30px; text-align:center; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.about-hero-image{border-radius:var(--border-radius); object-fit:cover; width:100%; height:100%;}
.grid-about-hero-image{grid-column-gap:30px; grid-row-gap:30px; border-radius:var(--border-radius); grid-template-rows:auto; grid-template-columns:1fr .5fr; grid-auto-columns:1fr; margin-top:80px; margin-bottom:50px; display:grid; overflow:hidden;}
.why-choose-item{border:1px solid var(--color--accent); border-radius:var(--border-radius); background-color:var(--color--accent); flex-direction:column; gap:30px; padding:30px; display:flex;}
.grid-why-choose-two{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.why-choose-icon{width:60px; height:60px;}
.why-choose-title-two{font-size:28px;}
.contact-hero-section{margin-top:50px;}
.grid-contact{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-top:50px; margin-bottom:30px; display:grid;}
/* Override for grid-contact when mt-0-important class is applied */
.grid-contact.mt-0-important {
  margin-top: 0 !important;
}
.grid-immigration-detail-split{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr .6fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.grid-immigration-detail-gallery{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-bottom:30px; display:grid;}
.button-full{width:100%;}
.sidebar-contact-title{color:var(--color--white); margin-bottom:0; font-size:24px;}
.grid-why-choose-split{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; display:grid;}
.country-detail-hero-image{object-fit:cover; border-radius:50%; width:100px; height:100px;}
.country-detail-hero-title{color:var(--color--white); margin-bottom:0; position:relative;}
.country-detail-hero-content{z-index:2; grid-column-gap:20px; grid-row-gap:20px; flex-flow:column; justify-content:flex-start; align-items:center; display:flex; position:relative;}
.country-detail-hero-overlay{background-color:var(--color--black); opacity:.6; width:102%; min-height:104%; margin-top:-1%; margin-left:-1%; position:absolute; inset:0%;}
.grid-countries{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.country-item{text-decoration:none;}
.country-image-wrap{border-radius:var(--border-radius); width:100%; margin-bottom:15px; position:relative; overflow:hidden;}
.country-image {
    -o-object-fit: cover;
    object-fit: cover;
    /* width: 100%; */
    /* height: 100%; */
    aspect-ratio: 10/7;
    transition: transform 0.5s ease;
}

.country-item:hover .country-image {
    transform: scale(1.05);
}
.country-title {
    margin-bottom: 0;
    font-size: 20px;
    color: #1c4928;
    position: absolute;
    z-index: 2;
    /* position: absolute; */
    inset: auto auto 16px 20px;
    background: #ffffff;
    padding: 6px 22px 6px 30px;
    border-radius: 20px;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

.country-item:hover .country-title {
    background-color: var(--color--prime);
    color: #ffffff;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes pulse-dot {
    0% {
        transform: translateY(-50%) scale(0.9);
        opacity: 0.8;
        box-shadow: 0 0 0 0 rgba(221, 104, 38, 0.3);
    }
    50% {
        transform: translateY(-50%) scale(1.1);
        opacity: 1;
        box-shadow: 0 0 0 3px rgba(221, 104, 38, 0.15);
    }
    100% {
        transform: translateY(-50%) scale(0.9);
        opacity: 0.8;
        box-shadow: 0 0 0 0 rgba(221, 104, 38, 0.3);
    }
}

.country-title::before {
    content: "";
    position: absolute;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: var(--color--prime);
    border-radius: 50%;
    animation: pulse-dot 3s ease-in-out infinite;
    will-change: transform, opacity, box-shadow;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.country-item:hover .country-title::before {
    background-color: #ffffff;
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    animation: none;
}

/* Pause animation when the page is not visible to save resources */
@media (prefers-reduced-motion: reduce) {
    .country-title::before {
        animation: none;
    }
}
.country-flag {
    z-index: 2;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    position: absolute;
    inset: 16px auto auto 20px;
}
/* .country-flag{z-index:2; object-fit:cover; border-radius:50%; width:50px; height:50px; position:absolute; inset:30px auto auto 30px;} */
.country-image-overlay{
    background-color: var(--color--black);
    opacity: 0;
    width: 102%;
    height: 102%;
    margin-top: -1%;
    margin-left: -1%;
    position: absolute;
    inset: 0%;
    transition: opacity 0.3s ease;
}

.country-item:hover .country-image-overlay {
    opacity: 0.2;
}
.sidebar-title-inline{color:var(--color--white); margin-bottom:0; font-size:28px;}
.sidebar-title-inline-wrap{grid-column-gap:5px; grid-row-gap:5px; flex-flow:wrap; display:flex;}
.contact-info{grid-column-gap:10px; grid-row-gap:10px; flex-flow:column; justify-content:flex-start; align-items:flex-start; display:flex;}
.contact-info a{transition: all .3s;}
.contact-info a:hover{color: var(--color--prime);}
.contact-item{grid-column-gap:50px; grid-row-gap:16px; border:1px solid var(--color--accent); border-radius:var(--border-radius); background-color:var(--color--accent); flex-flow:column; justify-content:space-between; padding:30px; display:flex;}
.contact-icon{width:50px; height:50px;}
.contact-info-title{margin-bottom:0; font-size:28px;}
.address-list{grid-column-gap:6px; grid-row-gap:6px; flex-flow:column; display:flex;}
.about-split-feature-title{color:var(--color--prime); font-size:30px;}
.grid-about-split-feature{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-top:50px; display:grid;}
.about-content-wrap{width:75%; margin-left:auto; margin-right:auto;}
.about-section-split{position:relative;}
.grid-agent{grid-column-gap:50px; grid-row-gap:50px; flex-flow:wrap; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; justify-content:center; align-items:flex-start; display:flex;}
.grid-testimonial{grid-column-gap:30px; grid-row-gap:30px; column-count:3; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; column-gap:30px; margin-bottom:50px; position:relative;}
.grid-case-detail-image{z-index:2; grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr .5fr; grid-auto-columns:1fr; display:grid; position:relative;}
.grid-case-detail{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr .4fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.agent-detail-image-wrap{border-radius:var(--border-radius--md); width:100%; overflow:hidden;}
.agent-detail-title{color:var(--color--black); margin-bottom:2px; font-size:32px;}
.agent-detail-info-list{grid-column-gap:15px; grid-row-gap:15px; flex-flow:wrap; margin-top:20px; display:flex;}
.agent-detail-contact-link {
  color: var(--color--prime);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.2em;
  transition: all .3s ease-in-out;
  text-decoration: underline;
  width: 30px;
  height: 30px;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  border: 1px solid #f4433633;
  transition: all .2s ease-in-out;
}
.agent-detail-contact-link:hover{color:#ffffff; -webkit-text-decoration-color:var(--color--transparent); text-decoration-color:var(--color--transparent); background: var(--color--prime);
}
/* .agent-detail-description{margin-bottom:auto;} */
.agent-detail-content-wrap{grid-column-gap:15px; grid-row-gap:15px; flex-flow:column; display:flex;}
.grid-agent-detail{z-index:2; grid-column-gap:30px; grid-row-gap:30px; border-radius:var(--border-radius); color:var(--color--body); grid-template-rows:auto; grid-template-columns:1fr 1fr; grid-auto-columns:1fr; margin-bottom:30px; padding:30px; display:grid; position:relative; overflow:hidden;}
.grid-case{grid-column-gap:30px; grid-row-gap:30px; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
.grid-visa-detail,.grid-country-detail{grid-column-gap:50px; grid-row-gap:50px; grid-template-rows:auto; grid-template-columns:1fr .6fr; grid-auto-columns:1fr; align-items:start; display:grid;}
.country-detail-wrap{border-radius:var(--border-radius); background-color:var(--color--black); text-align:center; background-image:url("https://d3e54v103j8qbb.cloudfront.net/img/background-image.svg"); background-position:50%; background-repeat:no-repeat; background-size:cover; margin-top:10px; margin-left:30px; margin-right:30px; padding:150px 50px; position:relative; overflow:hidden;}
.vector-01-image{opacity:.3;}
.text-black{color:var(--color--black);}
.about-content{grid-column-gap:20px; grid-row-gap:20px; flex-flow:column; display:flex;}
@media screen and (min-width:1280px){
  .button-black.nav-button{padding-left:40px; padding-right:40px;}
  .grid-footer,.grid-footer-contact{grid-column-gap:80px; grid-row-gap:80px;}
  .hero-image-wrap{margin-left:50px; margin-right:50px;}
  .about-content-right{width:75%;}
  .about-title-right{margin-bottom:50px; font-size:34px;}
  .grid-about-counter{grid-column-gap:80px; grid-row-gap:80px; margin-top:90px;}
  .about-counter-title{font-size:54px;}
  .vector-01{width:630px; height:630px;}
  .vector-01.sm{width:350px; height:350px; left:-180px;}
  .immigration-content{padding:30px;}
  .immigration-title{font-size:24px;}
  .grid-step{grid-column-gap:100px; grid-row-gap:100px;}
  .step-item.center{margin-top:40px;}
  .step-item.first{margin-top:0;}
  .step-number{margin-bottom:40px;}
  .vector-02{top:-10px; right:80px;}
  .grid-visa-inline{grid-column-gap:30px; grid-row-gap:30px; margin-top:60px;}
  .hero-content-left{padding-top:50px; padding-bottom:50px;}
  .hero-title-left{margin-bottom:50px; font-size:72px;}
  .grid-hero-info{grid-column-gap:50px; grid-row-gap:50px; margin-top:150px;}
  .hero-info-text{font-size:18px; line-height:1.4em;}
  .grid-immigration{grid-column-gap:50px; grid-row-gap:50px;}
  .grid-why-choose{grid-column-gap:230px; grid-row-gap:230px; margin-bottom:130px;}
  .grid-why-choose-counter{grid-column-gap:50px; grid-row-gap:50px;}
  .grid-why-choose-feature{grid-column-gap:50px; grid-row-gap:50px;}
  .grid-visa-split{grid-column-gap:50px; grid-row-gap:50px;}
  .vector-06{width:500px; height:500px;}
  .cta-content-wrap{width:60%; padding-top:50px; padding-bottom:50px; padding-left:50px;}
  .grid-contact-split{grid-column-gap:80px; grid-row-gap:80px;}
  .grid-contact-form-inner{grid-column-gap:30px;}
  .grid-about-hero-image{grid-column-gap:50px; grid-row-gap:50px;}
  .why-choose-item{grid-column-gap:30px; grid-row-gap:30px; padding:50px;}
  .grid-why-choose-two{grid-column-gap:50px; grid-row-gap:50px;}
  .grid-contact{grid-column-gap:80px; grid-row-gap:80px; margin-top:80px; margin-bottom:50px;}
  .grid-immigration-detail-split{grid-column-gap:80px; grid-row-gap:80px; grid-template-columns:1fr .5fr;}
  .grid-immigration-detail-gallery{grid-column-gap:50px; grid-row-gap:50px; margin-bottom:50px;}
  .grid-why-choose-split,.grid-countries{grid-column-gap:50px; grid-row-gap:50px;}
  .contact-item{padding:50px;}
  .grid-agent{grid-column-gap:80px; grid-row-gap:80px;}
  .grid-case-detail-image{grid-column-gap:50px; grid-row-gap:50px;}
  .grid-case{grid-column-gap:50px; grid-row-gap:50px;}
  .grid-visa-detail,.grid-country-detail{grid-column-gap:80px; grid-row-gap:80px; grid-template-columns:1fr .5fr;}
  .country-detail-wrap{padding:200px 100px;}
}
@media screen and (min-width:1440px){
  .grid-footer,.grid-footer-contact{grid-column-gap:100px; grid-row-gap:100px;}
  .vector-01.sm{width:400px; height:400px; left:-200px;}
  .step-item.center{margin-top:55px;}
  .step-item.first{margin-top:14px;}
  .hero-image-right-wrap{width:52%;}
  .grid-hero-info{grid-column-gap:100px; grid-row-gap:100px; margin-top:220px;}
  .vector-06{width:630px; height:630px;}
  .cta-content-wrap{width:50%; padding-top:100px; padding-bottom:100px; padding-left:100px;}
  .country-detail-wrap{margin-left:50px; margin-right:50px;}
}
@media screen and (min-width:1920px){
  .grid-footer,.grid-footer-contact{grid-column-gap:200px; grid-row-gap:200px;}
  .about-content-right{width:66%;}
  .vector-01{width:830px; height:830px; left:-220px;}
  .vector-01.sm{width:550px; height:550px;}
  .step-item.center{margin-top:75px;}
  .step-item.first{margin-top:20px;}
  .grid-visa-inline{grid-column-gap:40px; grid-row-gap:40px;}
}
@media screen and (max-width:991px){
  blockquote{padding:40px;}
  .section-spacing{padding-top:80px; padding-bottom:80px;}
  .section-spacing-top{padding-top:80px;}
  .section-spacing-bottom{padding-bottom:80px;}
  .hero-inner-content{padding:70px 50px;}
  .hero-inner-title{font-size:44px;}
  .menu-button{border:1px solid var(--color--secondary-1); border-radius:var(--border-radius); background-color:var(--color--accent); color:var(--color--black); text-align:center; justify-content:center; align-items:center; padding:14px;}
  .menu-button.w--open{border-color:var(--color--black); background-color:var(--color--prime); color:var(--color--white);}
  .navbar{padding-top:20px; padding-bottom:20px;}
  .footer{padding-top:80px; padding-bottom:30px;}
  .grid-footer-link-inner{grid-column-gap:10px; grid-row-gap:10px;}
  .grid-navbar{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr 1fr auto;}
  .grid-footer{grid-column-gap:30px; grid-row-gap:30px; grid-template-columns:1fr;}
  .nav-menu{border:1px solid var(--color--gary-3); border-radius:var(--border-radius--sm); background-color:var(--color--accent); margin-left:30px; margin-right:30px; padding:10px 20px;}
  .nav-link{margin:8px 0; display:block;}
  .grid-footer-contact{grid-column-gap:30px; grid-row-gap:30px; grid-template-columns:1fr 1fr; margin-top:30px; padding-bottom:30px;}
  .hero-section{margin-top:-100px; padding-top:160px;}
  .hero-content-center{width:100%;}
  .hero-title-center{margin-bottom:15px; font-size:60px;}
  .hero-button-list{margin-top:30px;}
  .hero-image-wrap{max-height:100%; margin-top:50px;}
  .hero-image{min-height:auto;}
  .hero-blur{height:500px;}
  .hero-blur-divider{height:350px; top:270px;}
  .about-section{padding-top:80px;}
  .about-content-right{width:100%;}
  .about-title-right{font-size:24px;}
  .vector-01{display:none;}
  .section-title{margin-bottom:40px;}
  .section-title.immigration-section-title{width:75%;}
  .immigration-arrow-wrap{width:44px; height:44px;}
  .immigration-arrow{width:14px; height:14px;}
  .grid-step{grid-column-gap:50px; grid-row-gap:50px; grid-template-columns:1fr;}
  .step-item.center{margin-top:0;}
  .step-number{margin-bottom:10px;}
  .vector-02{display:none;}
  .grid-visa-type{grid-template-columns:1fr;}
  .grid-visa-inline{grid-column-gap:30px; grid-row-gap:30px; flex-flow:wrap; grid-template-rows:auto; grid-template-columns:1fr 1fr 1fr; grid-auto-columns:1fr; display:grid;}
  .accordion-wrap{padding-top:20px; padding-bottom:20px;}
  .hero-image-right-wrap{border-radius:0; width:100%; margin-bottom:-2px; position:static;}
  .hero-content-left{width:100%;}
  .hero-title-left{font-size:60px;}
  .grid-hero-info{margin-top:30px;}
  .grid-immigration{grid-template-columns:1fr 1fr;}
  .button-center{margin-top:30px;}
  .grid-why-choose{grid-column-gap:30px; grid-row-gap:30px; grid-template-columns:1fr; margin-bottom:50px;}
  .grid-why-choose-feature,.grid-visa-split{grid-template-columns:1fr;}
  .cta-title{font-size:40px;}
  .vector-06{width:380px; height:380px;}
  .cta-feature-list{margin-top:15px; margin-bottom:20px;}
  .cta-content-wrap{width:52%;}
  .grid-contact-split{grid-template-columns:1fr;}
  .sidebar-sticky{position:static;}
  .sidebar-contact{grid-column-gap:15px; grid-row-gap:15px;}
  .section-spacing-sm{padding-top:80px; padding-bottom:80px;}
  .about-hero-title{font-size:56px;}
  .grid-about-hero-image{margin-top:50px; margin-bottom:50px;}
  .grid-why-choose-two{grid-template-columns:1fr 1fr;}
  .grid-contact{grid-template-columns:1fr;}
  .grid-immigration-detail-split{grid-template-columns:1fr;}
  .grid-immigration-detail-gallery{grid-column-gap:30px; grid-row-gap:30px;}
  .grid-countries{grid-template-columns:1fr 1fr;}
  .about-split-feature-title{margin-bottom:5px;}
  .grid-about-split-feature{margin-top:30px;}
  .about-content-wrap{grid-column-gap:30px; grid-row-gap:30px; width:100%;}
  .grid-agent{grid-column-gap:40px; grid-row-gap:40px;}
  .grid-testimonial{grid-column-gap:20px; grid-row-gap:20px; column-count:2; column-gap:30px; margin-bottom:30px;}
  .grid-case-detail{grid-column-gap:30px; grid-row-gap:30px; grid-template-columns:1fr;}
  .agent-detail-title{font-size:26px;}
  .agent-detail-info-list{grid-row-gap:10px;}
  .grid-case{grid-template-columns:1fr 1fr;}
  .grid-visa-detail,.grid-country-detail{grid-template-columns:1fr;}
  .about-content{grid-column-gap:10px; grid-row-gap:10px;}
}
@media screen and (max-width:767px){
  h1{font-size:48px;}
  h2{font-size:42px;}
  h3{font-size:36px;}
  h4{font-size:30px;}
  h5{font-size:24px;}
  h6{font-size:20px;}
  blockquote{padding:30px;}
  .container-medium{padding-left:20px; padding-right:20px;}
  .container,.container-small,.inner-container{padding-left:20px; padding-right:20px;}
  .section-spacing{padding-top:70px; padding-bottom:70px;}
  .section-spacing-top{padding-top:70px;}
  .section-spacing-bottom{padding-bottom:70px;}
  .hero-inner-content{padding:50px 30px;}
  .hero-inner-title{font-size:42px;}
  .nav-right{display:none;}
  .navbar{padding-top:15px; padding-bottom:15px;}
  .footer{padding-top:60px; padding-bottom:20px;}
  .grid-footer-link-wrap{grid-template-columns:1fr;}
  .grid-footer-link-inner{grid-column-gap:20px; grid-row-gap:10px;}
  .grid-navbar{grid-template-columns:1fr .5fr;}
  .nav-menu{margin-left:20px; margin-right:20px;}
  .grid-footer-link{grid-column-gap:30px; grid-row-gap:30px;}
  .hero-section{margin-top:-90px; padding-top:140px;}
  .hero-title-center{margin-bottom:15px; font-size:48px;}
  .hero-button-list{margin-top:20px;grid-column-gap: 20px;}
  .hero-image-wrap{margin-top:30px; margin-left:20px; margin-right:20px;}
  .hero-blur{height:400px;}
  .hero-blur-divider{height:250px; top:170px;}
  .about-section{padding-top:70px;}
  .about-title-right{margin-bottom:15px; font-size:20px;}
  .grid-about-counter{grid-column-gap:15px; grid-row-gap:15px; margin-top:30px;}
  .about-counter-item{font-size:16px; line-height:1.4em;}
  .about-counter-title{margin-bottom:5px; font-size:36px;}
  .section-title{margin-bottom:30px;}
  .section-title.immigration-section-title{width:100%;}
  .immigration-arrow-wrap{top:20px; right:20px;}
  .step-title{margin-bottom:10px;}
  .grid-visa-inline{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr 1fr;}
  .accordion-wrap{padding-top:15px; padding-bottom:15px;}
  .accordion-title{font-size:20px;}
  .accordion-content-wrap{font-size:16px; line-height:1.4em;}
  .accordion-heading{grid-column-gap:15px; grid-row-gap:15px;}
  .hero-content-left{margin-bottom:20px;}
  .hero-title-left{font-size:48px;}
  .grid-immigration{grid-template-columns:1fr;}
  .button-center{margin-top:20px;}
  .grid-why-choose{margin-bottom:30px;}
  .grid-why-choose-counter{grid-column-gap:20px; grid-row-gap:20px;}
  .grid-why-choose-feature{grid-column-gap:20px; grid-row-gap:20px;}
  .cta-wrap{border-radius:var(--border-radius--md);}
  .cta-title{font-size:34px;}
  .vector-06{display:none;}
  .cta-content-wrap{width:100%; padding:20px;}
  .grid-contact-form-inner{grid-column-gap:20px; grid-row-gap:5px; grid-template-columns:1fr;}
  .sidebar-item{border-radius:var(--border-radius--md);}
  .section-spacing-sm{padding-top:70px; padding-bottom:70px;}
  .about-hero-title{font-size:44px;}
  .grid-about-counter-center{grid-template-columns:1fr 1fr;}
  .about-hero-image{border-radius:var(--border-radius--md);}
  .grid-about-hero-image{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr .75fr;}
  .why-choose-item{grid-column-gap:30px; grid-row-gap:30px; border-radius:var(--border-radius--md); padding:20px;}
  .grid-why-choose-two{grid-template-columns:1fr;}
  .grid-immigration-detail-gallery{grid-column-gap:20px; grid-row-gap:20px;}
  .grid-why-choose-split{grid-template-columns:1fr;}
  .country-detail-hero-image{width:80px; height:80px;}
  .country-detail-hero-title{font-size:42px;}
  .country-detail-hero-content{grid-column-gap:15px; grid-row-gap:15px;}
  .country-image-wrap{border-radius:var(--border-radius--md);}
  .country-title{font-size:24px;}
  .country-flag{top:15px; left:15px;}
  .contact-item{grid-column-gap:30px; grid-row-gap:30px; border-radius:var(--border-radius--md);}
  .contact-icon{width:40px; height:40px;}
  .grid-about-split-feature,.grid-agent{grid-column-gap:30px; grid-row-gap:30px;}
  .grid-testimonial{column-count:1; grid-template-columns:1fr 1fr; margin-bottom:20px;}
  .grid-case-detail-image{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr;}
  .agent-detail-info-list{grid-column-gap:15px; margin-top:0;}
  .grid-agent-detail{border-radius:var(--border-radius--md); grid-template-columns:1fr; margin-bottom:20px; padding:20px;}
  .grid-case{grid-template-columns:1fr;}
  .grid-country-detail{grid-column-gap:30px; grid-row-gap:30px;}
  .country-detail-wrap{border-radius:var(--border-radius--md); margin-left:20px; margin-right:20px; padding-top:50px; padding-bottom:50px;}
}
@media screen and (max-width:479px){
  body{font-size:16px; line-height:1.3em;}
  h1{font-size:36px;}
  h2{font-size:30px;}
  h3{font-size:26px;}
  h4{font-size:24px;}
  h5{font-size:20px;}
  h6{font-size:18px;}
  label{margin-bottom:5px;}
  blockquote{padding:20px;}
  .container-medium{padding-left:15px; padding-right:15px;}
  .text-small{font-size:14px; line-height:1.4em;}
  .form-input,.form-input::placeholder{font-size:16px; line-height:1.4em; font-weight:400 !important; color:var(--color--gray-1) ;}
  .text-lead{font-size:18px; line-height:1.5em;}
  .button-black{padding:12px 30px; font-size:16px; line-height:1.4em;}
  .container,.container-small,.inner-container{padding-left:15px; padding-right:15px;}
  .section-spacing{padding-top:60px; padding-bottom:60px;}
  .section-spacing-top{padding-top:60px;}
  .section-spacing-bottom{padding-bottom:70px;}
  .hero-inner-content{padding:30px 20px;}
  .hero-inner-title{font-size:32px; line-height:1.2em;}
  .menu-button{padding:10px;}
  .navbar{padding-top:10px; padding-bottom:10px;}
  .footer{padding-top:40px; padding-bottom:15px;}
  .brand{height:60px;}
  .grid-navbar{grid-column-gap:10px; grid-row-gap:10px;}
  .grid-footer{grid-template-columns:1fr;}
  .contact-wrap{grid-column-gap:10px; grid-row-gap:10px;}
  .nav-menu{margin-left:15px; margin-right:15px; padding-left:15px; padding-right:15px;}
  .nav-link{font-size:16px; line-height:1.5em;}
  .button-gradient{padding:12px 26px; font-size:14px; line-height:1.4em;}
  .grid-footer-contact{grid-template-columns:1fr; padding-bottom:15px;}
  .hero-section{margin-top:-80px; padding-top:120px;}
  .hero-title-center{font-size:36px; line-height:1.2em; margin-bottom:10px;}
  .button-icon{width:18px; height:18px;}
  .button-secondary-2-outline{padding:12px 26px; font-size:14px; line-height:1.4em;}
  .hero-image-wrap{margin-left:15px; margin-right:15px;}
  .about-section{padding-top:60px;}
  .about-title-right{font-size:18px; line-height:1.3em;}
  .grid-about-counter{grid-column-gap:24px; grid-row-gap:24px; grid-template-columns:1fr; margin-top:24px;}
  .about-counter-title{font-size:30px;}
  .section-title{margin-bottom:20px;}
  .immigration-item{border-radius:var(--border-radius--md);}
  .immigration-content{padding:15px;}
  .immigration-title{margin-bottom:10px; font-size:20px;}
  .immigration-arrow-wrap{top:15px; right:15px;}
  .grid-step{grid-column-gap:30px; grid-row-gap:30px;}
  .step-number{width:50px; height:50px; margin-bottom:5px; font-size:22px; line-height:1.4em;}
  .step-title{font-size:24px;}
  .grid-visa-inline{grid-column-gap:20px; grid-row-gap:20px; grid-template-columns:1fr; margin-top:15px;}
  .accordion-wrap{font-size:14px; line-height:1.5em;}
  .accordion-title{font-size:18px;}
  .accordion-heading{grid-column-gap:12px; grid-row-gap:12px;}
  .accordion-content{font-size:14px; line-height:1.4em;}
  .button-link{font-size:16px; line-height:1.3em;}
  .hero-content-left{margin-bottom:15px;}
  .hero-title-left{font-size:40px;}
  .grid-hero-info{grid-template-columns:1fr;}
  .hero-info-icon-wrap{width:50px; height:50px; margin-bottom:10px;}
  .button-center{margin-top:15px;}
  .grid-why-choose-counter{grid-template-columns:1fr;}
  .cta-title{font-size:28px;}
  .cta-content-wrap{padding:15px;}
  .sidebar-item{padding:20px;}
  .sidebar-contact{grid-column-gap:10px; grid-row-gap:10px;}
  .sidebar-item-inner{margin-bottom:20px; padding-bottom:30px;}
  .sidebar-item-inner .form-input{margin-bottom:10px;}
  .sidebar-item-inner .button-gradient{width:100%;}
  .section-spacing-sm{padding-top:60px; padding-bottom:60px;}
  .about-hero-title{font-size:40px;}
  .grid-about-hero-image{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr; margin-top:30px; margin-bottom:30px;}
  .why-choose-item{padding:15px;}
  .why-choose-icon{width:50px; height:50px;}
  .why-choose-title-two{font-size:24px;}
  .grid-immigration-detail-gallery{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr;}
  .sidebar-contact-title{font-size:22px;}
  .country-detail-hero-image{width:60px; height:60px;}
  .country-detail-hero-title{font-size:38px;}
  .country-detail-hero-content{grid-column-gap:5px; grid-row-gap:5px;}
  .grid-countries{grid-template-columns:1fr;}
  .country-image-wrap{margin-bottom:10px;}
  .sidebar-title-inline{font-size:22px;}
  .contact-item{grid-column-gap:20px; grid-row-gap:20px; padding:20px;}
  .contact-icon{width:30px; height:30px;}
  .contact-info-title{font-size:22px;}
  .about-split-feature-title{font-size:24px;}
  .grid-about-split-feature{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr;}
  .grid-agent{grid-column-gap:15px; grid-row-gap:30px;}
  .grid-testimonial{grid-column-gap:15px; grid-row-gap:15px; grid-template-columns:1fr; margin-bottom:15px;}
  .grid-case-detail-image{grid-column-gap:15px; grid-row-gap:15px;}
  .agent-detail-info-list{grid-column-gap:10px; grid-row-gap:10px; margin-top:10px; margin-bottom:10px;}
  .agent-detail-content-wrap{grid-column-gap:10px; grid-row-gap:10px;}
  .grid-agent-detail{grid-column-gap:15px; grid-row-gap:15px; margin-bottom:15px; padding:15px;}
  .grid-country-detail{grid-column-gap:20px; grid-row-gap:20px;}
  .country-detail-wrap{margin-left:15px; margin-right:15px; padding:30px;}
}
#w-node-bd6a3f5e-7429-54a5-9294-201eb705bfa1-5c59b398{justify-self:start;}
#w-node-_7c3bf295-6060-045d-7271-bf484b242481-ecf59ae5{grid-area:span 1 / span 1 / span 1 / span 1;}
#w-node-_63067e08-366b-f93d-ca5a-9cb96c243fdd-2ccf17b1{justify-self:start;}
#w-node-e551e49f-aeab-d0a0-3375-8de75a269063-2ccf17b1{justify-self:center;}
#w-node-_63067e08-366b-f93d-ca5a-9cb96c243ffe-2ccf17b1{justify-self:end;}
#w-node-_77b51261-d3a7-97c5-39dc-7a4a47472fc7-87687d97,#w-node-_77b51261-d3a7-97c5-39dc-7a4a47472fc7-dd74d3aa{grid-area:span 1 / span 2 / span 1 / span 2;}
@media screen and (max-width:991px){
  #w-node-e551e49f-aeab-d0a0-3375-8de75a269063-2ccf17b1{justify-self:auto;}
  #w-node-_63067e08-366b-f93d-ca5a-9cb96c244005-2ccf17b1{justify-self:end;}
  #w-node-_63bdca00-d749-298a-957d-ccf9955bcc13-dd74d3aa{order:-9999;}
}
@media screen and (max-width:767px){
  #w-node-_77b51261-d3a7-97c5-39dc-7a4a47472fc7-87687d97,#w-node-_77b51261-d3a7-97c5-39dc-7a4a47472fc7-dd74d3aa{grid-column:span 1 / span 1;}
  #w-node-_9d96d2b5-858d-8256-5e36-274d54252a9b-6bc3288d{grid-area:span 1 / span 1 / span 1 / span 1;}
}
@media screen and (max-width:479px){
  #w-node-_7c3bf295-6060-045d-7271-bf484b242481-ecf59ae5,#w-node-_07b30d10-699d-b2dc-c1b2-f6cde3af0c94-ecf59ae5,#w-node-d70db279-fe22-48cf-776b-954ca665c3ed-ecf59ae5{grid-column:span 1 / span 1;}
}
@font-face{font-family:'Helveticanowdisplay'; src:url('https://cdn.prod.website-files.com/6777c6ca4cd4fd1a5c59b396/6777d78a2bab4e39e382e3dc_HelveticaNowDisplay-Medium.woff2') format('woff2'); font-weight:500; font-style:normal; font-display:swap;}


/* Taimoor Styles */
.line{
  margin: 0 0 10px 10px;
}



.richText img {
        margin-block: 2em;
}
.richText strong {
    font-weight: 500;
    margin: 0 0 10px;
}
.richText p {

    margin: 0 0 40px;
}
.richText ul {
    list-style: disc;
    padding-left: 30px;
    margin: 0 0 40px;
}
.richText ul li{
    list-style: disc;
    margin: 0 0 10px;
}
.richText ul li p {
    margin: 0;
}
